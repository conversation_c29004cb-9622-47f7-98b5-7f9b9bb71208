<?php
// Test JWT functionality
require_once 'vendor/autoload.php';
require_once 'app/utils/JWTHandler.php';

echo "<h1>JWT Test</h1>";

try {
    // Test JWT Handler
    $jwtHandler = new JWTHandler();
    echo "<h2>✅ J<PERSON><PERSON>andler loaded successfully</h2>";
    
    // Test encode
    $testData = [
        'id' => 1,
        'username' => 'admin',
        'role' => 'admin'
    ];
    
    $token = $jwtHandler->encode($testData);
    echo "<h3>Encoded Token:</h3>";
    echo "<p style='word-break: break-all; background: #f0f0f0; padding: 10px;'>$token</p>";
    
    // Test decode
    $decoded = $jwtHandler->decode($token);
    echo "<h3>Decoded Data:</h3>";
    echo "<pre>" . print_r($decoded, true) . "</pre>";
    
    if ($decoded && $decoded['username'] === 'admin') {
        echo "<h2 style='color: green;'>✅ JWT encode/decode working correctly!</h2>";
    } else {
        echo "<h2 style='color: red;'>❌ JWT decode failed!</h2>";
    }
    
} catch (Exception $e) {
    echo "<h2 style='color: red;'>❌ Error: " . $e->getMessage() . "</h2>";
    echo "<p>Make sure you have run: <code>composer install</code></p>";
}

// Test composer autoload
echo "<h2>Composer Dependencies:</h2>";
if (class_exists('Firebase\JWT\JWT')) {
    echo "<p>✅ Firebase JWT library loaded</p>";
} else {
    echo "<p>❌ Firebase JWT library not found</p>";
}

if (file_exists('vendor/autoload.php')) {
    echo "<p>✅ Composer autoload.php exists</p>";
} else {
    echo "<p>❌ Composer autoload.php not found</p>";
}

echo "<h2>Next Steps:</h2>";
echo "<ul>";
echo "<li>✅ JWT authentication is ready</li>";
echo "<li>✅ Test with Postman using the guide</li>";
echo "<li>✅ Public APIs work without token</li>";
echo "<li>✅ Protected APIs require JWT token</li>";
echo "</ul>";
?>
