
<?php include 'app/views/shares/header.php'; ?> 

<h1><PERSON><PERSON><PERSON> sản phẩm</h1>

<div class="alert alert-danger d-none" id="error-message"></div>
<div class="alert alert-success d-none" id="success-message"></div>

<form id="edit-product-form"> 
    <input type="hidden" id="id" name="id">
    
    <div class="form-group mb-3"> 
        <label for="name">Tên sản phẩm:</label> 
        <input type="text" id="name" name="name" class="form-control" required minlength="3">
        <div class="invalid-feedback">Vui lòng nhập tên sản phẩm (ít nhất 3 ký tự)</div>
    </div> 
    
    <div class="form-group mb-3">
        <label for="description">Mô tả:</label>
        <textarea id="description" name="description" class="form-control" placeholder="<PERSON><PERSON> tả sản phẩm (t<PERSON><PERSON> chọn)"></textarea>
        <div class="invalid-feedback"><PERSON><PERSON> tả không hợp lệ</div>
    </div>

    <div class="form-group mb-3">
        <label for="price">Giá:</label>
        <input type="number" id="price" name="price" class="form-control" step="1000" required>
        <div class="invalid-feedback">Vui lòng nhập giá hợp lệ (số không âm)</div>
    </div>
    
    <div class="form-group mb-3"> 
        <label for="category_id">Danh mục:</label> 
        <select id="category_id" name="category_id" class="form-control" required> 
            <option value="">-- Chọn danh mục --</option>
        </select>
        <div class="invalid-feedback">Vui lòng chọn danh mục</div>
    </div> 
    
    <button type="submit" class="btn btn-primary" id="submit-btn">
        <span class="spinner-border spinner-border-sm d-none" id="loading-spinner"></span>
        Lưu thay đổi
    </button>
</form> 

<a href="/webbanhang/Product/list" class="btn btn-secondary mt-2">Quay lại danh sách sản phẩm</a>

<?php include 'app/views/shares/footer.php'; ?> 

<script> 
document.addEventListener("DOMContentLoaded", function() {
    const form = document.getElementById('edit-product-form');
    const submitBtn = document.getElementById('submit-btn');
    const loadingSpinner = document.getElementById('loading-spinner');
    const errorMessage = document.getElementById('error-message');
    const successMessage = document.getElementById('success-message');
    
    function showLoading() {
        submitBtn.disabled = true;
        loadingSpinner.classList.remove('d-none');
    }

    function hideLoading() {
        submitBtn.disabled = false;
        loadingSpinner.classList.add('d-none');
    }

    function showError(message) {
        errorMessage.textContent = message;
        errorMessage.classList.remove('d-none');
        successMessage.classList.add('d-none');
    }

    function showSuccess(message) {
        successMessage.textContent = message;
        successMessage.classList.remove('d-none');
        errorMessage.classList.add('d-none');
    }

    const productId = <?= $editId ?>;

    // Load product data
    showLoading();
    const token = localStorage.getItem('jwtToken');
    const headers = {};
    if (token) {
        headers['Authorization'] = `Bearer ${token}`;
    }

    Promise.all([
        fetch(`/webbanhang/api/product/${productId}`, { headers }).then(res => res.json()),
        fetch('/webbanhang/api/category', { headers }).then(res => res.json())
    ])
    .then(([product, categories]) => {
        // Fill product data
        document.getElementById('id').value = product.id;
        document.getElementById('name').value = product.name;
        document.getElementById('description').value = product.description;
        document.getElementById('price').value = product.price;

        // Fill categories
        const categorySelect = document.getElementById('category_id');
        categories.forEach(category => {
            const option = document.createElement('option');
            option.value = category.id;
            option.textContent = category.name;
            categorySelect.appendChild(option);
        });
        categorySelect.value = product.category_id;
    })
    .catch(error => {
        showError('Lỗi tải dữ liệu: ' + error.message);
    })
    .finally(() => {
        hideLoading();
    });

    form.addEventListener('submit', function(event) {
        event.preventDefault();
        
        // Reset messages
        errorMessage.classList.add('d-none');
        successMessage.classList.add('d-none');

        // Form validation
        if (!form.checkValidity()) {
            form.classList.add('was-validated');
            return;
        }

        const formData = new FormData(this);
        const jsonData = Object.fromEntries(formData.entries());
        jsonData.price = Number(jsonData.price);

        showLoading();

        const token = localStorage.getItem('jwtToken');
        fetch(`/webbanhang/api/product/${jsonData.id}`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${token}`
            },
            body: JSON.stringify(jsonData)
        })
        .then(response => {
            if (!response.ok) throw new Error('Lỗi khi cập nhật sản phẩm');
            return response.json();
        })
        .then(data => {
            if (data.message === 'Product updated successfully') {
                showSuccess('Cập nhật sản phẩm thành công!');
                setTimeout(() => {
                    location.href = '/webbanhang/Product';
                }, 1000);
            } else {
                throw new Error(data.error || 'Cập nhật sản phẩm thất bại');
            }
        })
        .catch(error => {
            showError(error.message);
        })
        .finally(() => {
            hideLoading();
        });
    });
}); 
</script>