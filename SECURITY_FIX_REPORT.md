# 🔒 Báo cáo sửa lỗi bảo mật - WEBBANHANG

## ❌ Vấn đề đã phát hiện

**Người dùng với role "user" c<PERSON> thể thêm, sử<PERSON>, x<PERSON><PERSON> sản phẩm** mặc dù chỉ admin mới được phép thực hiện các thao tác này.

### Nguyên nhân chính:

1. **JWT Token thiếu thông tin role**: Token chỉ chứa `id` và `username`, không có `role`
2. **API không có xác thực**: Các endpoint API không kiểm tra JWT token và role
3. **Frontend không kiểm tra quyền**: Hiển thị nút thêm/sửa/xóa cho tất cả user
4. **Web interface và API không đồng bộ**: Session và JWT authentication riêng biệt

## ✅ C<PERSON><PERSON> thay đổi đã thực hiện

### 1. Cậ<PERSON> nhật JWT Token
**File**: `app/controllers/AccountController.php`
```php
// TRƯỚC
$token = $this->jwtHandler->encode([
    'id' => $user->id,
    'username' => $user->username
]);

// SAU
$token = $this->jwtHandler->encode([
    'id' => $user->id,
    'username' => $user->username,
    'role' => $user->role  // ← THÊM ROLE
]);
```

### 2. Tạo JWT Middleware
**File**: `app/middleware/JWTMiddleware.php`
- Xác thực JWT token từ Authorization header
- Kiểm tra role admin
- Trả về response lỗi chuẩn

### 3. Cập nhật ProductApiController
**File**: `app/controllers/ProductApiController.php`
- **GET /api/product**: Yêu cầu đăng nhập
- **POST /api/product**: Chỉ admin
- **PUT /api/product/{id}**: Chỉ admin  
- **DELETE /api/product/{id}**: Chỉ admin

### 4. Cập nhật ProductController (Web)
**File**: `app/controllers/ProductController.php`
- Hỗ trợ cả Session và JWT authentication
- Method `isAdmin()` kiểm tra cả hai cách xác thực

### 5. Thêm Web Login
**File**: `app/controllers/AccountController.php`
- Method `processLogin()`: Tạo PHP Session
- Method `checkLogin()`: Trả về JWT token

### 6. Cập nhật Frontend
**Files**: `app/views/product/list.php`, `add.php`, `edit.php`
- Ẩn nút "Thêm sản phẩm mới" cho user thường
- Ẩn nút "Sửa" và "Xóa" cho user thường
- Thêm Authorization header cho API calls

## 🧪 Cách test

### 1. Tạo tài khoản test
```sql
-- Chạy file insert_test_user.sql
INSERT INTO account (username, password, fullname, role)
VALUES ('user', '$2y$10$VQYaPlxux7CLNC8mdJ0LeuWxP8cqviVVP4KD0VFaB6VgvBgGRMq5e', 'Test User', 'user');
```

### 2. Test đăng nhập Web (Session)
- Truy cập: `http://localhost/webbanhang/account/login`
- Hoặc sử dụng: `test_login.html`
- Đăng nhập với admin: `admin/admin123`
- Sau đó truy cập: `http://localhost/webbanhang/Product/add`
- ✅ **Kết quả mong đợi**: Admin có thể truy cập trang thêm sản phẩm

### 3. Test đăng nhập API (JWT)
- Sử dụng `test_login.html` hoặc `test_security.html`
- Đăng nhập API với admin: `admin/admin123`
- Test các API calls
- ✅ **Kết quả mong đợi**: API trả về dữ liệu thành công

### 4. Test với tài khoản user
- Username: `user` 
- Password: `admin123`
- KHÔNG thấy nút thêm/sửa/xóa trong giao diện
- Gọi API sẽ trả về lỗi 403 Forbidden

### 5. Test Postman
```
DELETE http://localhost/webbanhang/api/product/19
Headers:
- Content-Type: application/json
- Authorization: Bearer YOUR_JWT_TOKEN

Body: {} (để trống)
```

## 🎯 Kết quả

✅ **User thường không thể thêm sản phẩm**
✅ **User thường không thể sửa sản phẩm**  
✅ **User thường không thể xóa sản phẩm**
✅ **Admin vẫn có đầy đủ quyền**
✅ **Frontend ẩn các nút không phù hợp với role**
✅ **API trả về lỗi rõ ràng khi không có quyền**
✅ **Hỗ trợ cả Session và JWT authentication**

## 📁 Files test hỗ trợ

- `test_login.html`: Test cả 2 cách đăng nhập
- `test_security.html`: Test bảo mật API
- `insert_test_user.sql`: Tạo user test

## 🔐 Bảo mật bổ sung

### Đã thực hiện:
- JWT token có thời hạn (1 giờ)
- Xác thực token ở mọi API endpoint quan trọng
- Kiểm tra role cả frontend và backend
- Response lỗi không tiết lộ thông tin nhạy cảm
- Hỗ trợ cả Session và JWT authentication

### Khuyến nghị thêm:
- Thêm rate limiting cho API
- Log các hoạt động admin
- Thêm CSRF protection
- Sử dụng HTTPS trong production
