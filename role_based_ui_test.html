<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Role-Based UI Test</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-4">
        <h1>🔐 Role-Based UI Test</h1>
        
        <!-- Login Section -->
        <div class="card mb-4">
            <div class="card-header">
                <h5>🔑 Login & Role Test</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4">
                        <button class="btn btn-primary w-100 mb-2" onclick="loginAs('admin', 'admin123')">Login as Admin</button>
                    </div>
                    <div class="col-md-4">
                        <button class="btn btn-secondary w-100 mb-2" onclick="loginAs('user', 'admin123')">Login as User</button>
                    </div>
                    <div class="col-md-4">
                        <button class="btn btn-outline-danger w-100 mb-2" onclick="logout()">Logout</button>
                    </div>
                </div>
                <div id="loginStatus" class="mt-3"></div>
            </div>
        </div>

        <!-- UI Test Section -->
        <div class="card mb-4">
            <div class="card-header">
                <h5>🌐 UI Visibility Test</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6>Product Pages</h6>
                        <a href="/webbanhang/Product" class="btn btn-outline-info w-100 mb-2" target="_blank">Product List</a>
                        <a href="/webbanhang/Product/add" class="btn btn-outline-success w-100 mb-2" target="_blank">Add Product (Admin Only)</a>
                    </div>
                    <div class="col-md-6">
                        <h6>Category Pages</h6>
                        <a href="/webbanhang/Category" class="btn btn-outline-info w-100 mb-2" target="_blank">Category List</a>
                        <a href="/webbanhang/Category/add" class="btn btn-outline-success w-100 mb-2" target="_blank">Add Category (Admin Only)</a>
                    </div>
                </div>
                <div class="alert alert-info mt-3">
                    <strong>Test Instructions:</strong><br>
                    1. Login as <strong>Admin</strong> → Should see "Add" buttons and Edit/Delete buttons<br>
                    2. Login as <strong>User</strong> → Should NOT see any Add/Edit/Delete buttons<br>
                    3. Try accessing add pages directly when logged in as User → Should be blocked
                </div>
            </div>
        </div>

        <!-- Expected Behavior -->
        <div class="card mb-4">
            <div class="card-header">
                <h5>📋 Expected Behavior</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6 class="text-success">✅ Admin Role</h6>
                        <ul class="list-group list-group-flush">
                            <li class="list-group-item">See "Thêm sản phẩm" button</li>
                            <li class="list-group-item">See "Thêm danh mục" button</li>
                            <li class="list-group-item">See "Sửa" and "Xóa" buttons on items</li>
                            <li class="list-group-item">Can access /Product/add and /Category/add</li>
                            <li class="list-group-item">Navigation shows admin links</li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h6 class="text-warning">⚠️ User Role</h6>
                        <ul class="list-group list-group-flush">
                            <li class="list-group-item">NO "Thêm sản phẩm" button</li>
                            <li class="list-group-item">NO "Thêm danh mục" button</li>
                            <li class="list-group-item">NO "Sửa" and "Xóa" buttons on items</li>
                            <li class="list-group-item">Cannot access /Product/add and /Category/add</li>
                            <li class="list-group-item">Navigation hides admin links</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>

        <!-- Current Status -->
        <div class="card">
            <div class="card-header">
                <h5>📊 Current Status</h5>
            </div>
            <div class="card-body">
                <div id="statusInfo"></div>
                <button class="btn btn-outline-primary" onclick="checkStatus()">Refresh Status</button>
            </div>
        </div>
    </div>

    <script>
        // Decode JWT token
        function decodeJWT(token) {
            try {
                const base64Url = token.split('.')[1];
                const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');
                const jsonPayload = decodeURIComponent(atob(base64).split('').map(function(c) {
                    return '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2);
                }).join(''));
                return JSON.parse(jsonPayload);
            } catch (error) {
                return null;
            }
        }

        // Login function
        async function loginAs(username, password) {
            try {
                const response = await fetch('/webbanhang/account/checkLogin', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ username, password })
                });

                const data = await response.json();
                
                if (data.token) {
                    localStorage.setItem('jwtToken', data.token);
                    const decoded = decodeJWT(data.token);
                    document.getElementById('loginStatus').innerHTML = 
                        `<div class="alert alert-success">
                            ✅ Logged in as <strong>${username}</strong><br>
                            Role: <strong>${decoded.data.role}</strong><br>
                            <small>Now open the Product/Category pages in new tabs to see the UI changes!</small>
                        </div>`;
                    checkStatus();
                } else {
                    document.getElementById('loginStatus').innerHTML = 
                        `<div class="alert alert-danger">❌ Login failed: ${data.message}</div>`;
                }
            } catch (error) {
                document.getElementById('loginStatus').innerHTML = 
                    `<div class="alert alert-danger">❌ Error: ${error.message}</div>`;
            }
        }

        // Logout function
        function logout() {
            localStorage.removeItem('jwtToken');
            document.getElementById('loginStatus').innerHTML = 
                '<div class="alert alert-info">Logged out. UI should now hide admin features.</div>';
            checkStatus();
        }

        // Check current status
        function checkStatus() {
            const token = localStorage.getItem('jwtToken');
            let statusHTML = '<h6>Authentication Status:</h6>';
            
            if (token) {
                const decoded = decodeJWT(token);
                if (decoded) {
                    const isAdminRole = decoded.data.role === 'admin';
                    const roleClass = isAdminRole ? 'success' : 'warning';
                    const roleIcon = isAdminRole ? '👑' : '👤';
                    
                    statusHTML += `
                        <div class="alert alert-${roleClass}">
                            ${roleIcon} <strong>Logged in as:</strong> ${decoded.data.username}<br>
                            <strong>Role:</strong> ${decoded.data.role}<br>
                            <strong>Expires:</strong> ${new Date(decoded.exp * 1000).toLocaleString()}<br>
                            <strong>UI Features:</strong> ${isAdminRole ? 'Full Access (Admin)' : 'Read Only (User)'}
                        </div>`;
                } else {
                    statusHTML += '<div class="alert alert-warning">Invalid token</div>';
                }
            } else {
                statusHTML += '<div class="alert alert-secondary">Not logged in - All admin features hidden</div>';
            }
            
            document.getElementById('statusInfo').innerHTML = statusHTML;
        }

        // Check status on page load
        checkStatus();
    </script>
</body>
</html>
