
<?php include 'app/views/shares/header.php'; ?>

<div class="container">
    <div class="mb-4">
        <h1 class="display-6 fw-bold text-primary">
            <i class="fas fa-plus-circle me-3"></i>Thêm sản phẩm mới
        </h1>
        <p class="text-muted">Đ<PERSON><PERSON>n thông tin chi tiết để thêm sản phẩm vào hệ thống</p>
    </div>

    <div class="row justify-content-center">
        <div class="col-12 col-lg-10 col-xl-8">
            <div class="card shadow-sm border-0" style="border-radius: 1rem;">
                <div class="card-header bg-success text-white" style="border-radius: 1rem 1rem 0 0;">
                    <h5 class="mb-0">
                        <i class="fas fa-info-circle me-2"></i>Thông tin sản phẩm
                    </h5>
                </div>
                <div class="card-body p-4">
                    <div class="alert alert-danger d-none" id="error-message"></div>
                    <div class="alert alert-success d-none" id="success-message"></div>

                    <form id="add-product-form" novalidate>
                        <div class="row">
                            <div class="col-md-8">
                                <div class="mb-4">
                                    <label for="name" class="form-label fw-semibold">
                                        <i class="fas fa-tag me-2 text-primary"></i>
                                        Tên sản phẩm <span class="text-danger">*</span>
                                    </label>
                                    <input type="text" id="name" name="name" class="form-control form-control-lg"
                                           required minlength="3" placeholder="Nhập tên sản phẩm">
                                    <div class="invalid-feedback">Vui lòng nhập tên sản phẩm (ít nhất 3 ký tự)</div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-4">
                                    <label for="category_id" class="form-label fw-semibold">
                                        <i class="fas fa-folder me-2 text-success"></i>
                                        Danh mục <span class="text-danger">*</span>
                                    </label>
                                    <select id="category_id" name="category_id" class="form-select form-select-lg" required>
                                        <option value="">-- Chọn danh mục --</option>
                                    </select>
                                    <div class="invalid-feedback">Vui lòng chọn danh mục</div>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-4">
                                    <label for="price" class="form-label fw-semibold">
                                        <i class="fas fa-dollar-sign me-2 text-warning"></i>
                                        Giá bán <span class="text-danger">*</span>
                                    </label>
                                    <div class="input-group input-group-lg">
                                        <input type="number" id="price" name="price" class="form-control"
                                               step="1000" required placeholder="0">
                                        <span class="input-group-text">VNĐ</span>
                                    </div>
                                    <div class="invalid-feedback">Vui lòng nhập giá hợp lệ (phải là số)</div>
                                    <div class="form-text">Giá tính bằng VNĐ, bước nhảy 1,000</div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-4">
                                    <label for="stock" class="form-label fw-semibold">
                                        <i class="fas fa-boxes me-2 text-info"></i>
                                        Số lượng tồn kho
                                    </label>
                                    <input type="number" id="stock" name="stock" class="form-control form-control-lg"
                                           min="0" placeholder="0">
                                    <div class="form-text">Để trống nếu không quản lý tồn kho</div>
                                </div>
                            </div>
                        </div>

                        <div class="mb-4">
                            <label for="description" class="form-label fw-semibold">
                                <i class="fas fa-align-left me-2 text-secondary"></i>
                                Mô tả sản phẩm
                            </label>
                            <textarea id="description" name="description" class="form-control" rows="6"
                                      placeholder="Nhập mô tả chi tiết về sản phẩm..."></textarea>
                            <div class="form-text">Mô tả chi tiết giúp khách hàng hiểu rõ hơn về sản phẩm</div>
                        </div>

                        <hr class="my-4">

                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <small class="text-muted">
                                    <i class="fas fa-info-circle me-1"></i>
                                    Các trường có dấu <span class="text-danger">*</span> là bắt buộc
                                </small>
                            </div>
                            <div class="d-flex gap-2">
                                <a href="/webbanhang/Product" class="btn btn-outline-secondary btn-lg px-4">
                                    <i class="fas fa-times me-2"></i>Hủy
                                </a>
                                <button type="submit" class="btn btn-success btn-lg px-4" id="submit-btn">
                                    <span class="spinner-border spinner-border-sm d-none me-2" id="loading-spinner"></span>
                                    <i class="fas fa-plus me-2"></i>Thêm sản phẩm
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<?php include 'app/views/shares/footer.php'; ?>

<script>
document.addEventListener("DOMContentLoaded", function() {
    const form = document.getElementById('add-product-form');
    const submitBtn = document.getElementById('submit-btn');
    const loadingSpinner = document.getElementById('loading-spinner');
    const errorMessage = document.getElementById('error-message');
    const successMessage = document.getElementById('success-message');
    const categorySelect = document.getElementById('category_id');

    function showLoading() {
        submitBtn.disabled = true;
        loadingSpinner.classList.remove('d-none');
    }

    function hideLoading() {
        submitBtn.disabled = false;
        loadingSpinner.classList.add('d-none');
    }

    function showError(message) {
        errorMessage.textContent = message;
        errorMessage.classList.remove('d-none');
        successMessage.classList.add('d-none');
    }

    function showSuccess(message) {
        successMessage.textContent = message;
        successMessage.classList.remove('d-none');
        errorMessage.classList.add('d-none');
    }

    // Load categories
    fetch('/webbanhang/api/category')
        .then(response => {
            if (!response.ok) throw new Error('Không thể tải danh mục');
            return response.json();
        })
        .then(categories => {
            categories.forEach(category => {
                const option = document.createElement('option');
                option.value = category.id;
                option.textContent = category.name;
                categorySelect.appendChild(option);
            });
        })
        .catch(error => showError('Lỗi tải danh mục: ' + error.message));

    form.addEventListener('submit', function(event) {
        event.preventDefault();
        
        // Reset messages
        errorMessage.classList.add('d-none');
        successMessage.classList.add('d-none');

        // Form validation
        if (!form.checkValidity()) {
            form.classList.add('was-validated');
            return;
        }

        const formData = new FormData(this);
        const jsonData = Object.fromEntries(formData.entries());
        
        // Convert price to number
        jsonData.price = Number(jsonData.price);

        showLoading();

        const token = localStorage.getItem('jwtToken');
        fetch('/webbanhang/api/product', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${token}`
            },
            body: JSON.stringify(jsonData)
        })
        .then(response => {
            if (!response.ok) throw new Error('Lỗi khi thêm sản phẩm');
            return response.json();
        })
        .then(data => {
            if (data.message === 'Product created successfully') {
                showSuccess('Thêm sản phẩm thành công!');
                setTimeout(() => {
                    location.href = '/webbanhang/Product';
                }, 1000);
            } else {
                throw new Error(data.error || 'Thêm sản phẩm thất bại');
            }
        })
        .catch(error => {
            showError(error.message);
        })
        .finally(() => {
            hideLoading();
        });
    });
});
</script>