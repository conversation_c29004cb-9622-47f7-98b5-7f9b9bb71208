<?php

require_once('app/config/database.php');
require_once('app/models/CategoryModel.php');
require_once('app/middleware/JWTMiddleware.php');

class CategoryApiController
{
    private $categoryModel;
    private $db;
    private $jwtMiddleware;

    public function __construct()
    {
        // Bắt đầu output buffering để tránh output không mong muốn
        if (!ob_get_level()) {
            ob_start();
        }

        $this->db = (new Database())->getConnection();
        $this->categoryModel = new CategoryModel($this->db);
        $this->jwtMiddleware = new JWTMiddleware();
    }

    private function cleanOutput()
    {
        // <PERSON><PERSON><PERSON> bấ<PERSON> kỳ output buffer nào trước khi trả về JSON
        while (ob_get_level()) {
            ob_end_clean();
        }
    }

    // Lấy danh sách danh mục (public - không cần auth)
    public function index()
    {
        $this->cleanOutput();
        header('Content-Type: application/json; charset=utf-8');

        try {
            $categories = $this->categoryModel->getCategories();

            // <PERSON><PERSON><PERSON> bảo encoding UTF-8
            $json = json_encode($categories, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);

            if ($json === false) {
                throw new Exception('JSON encoding failed: ' . json_last_error_msg());
            }

            echo $json;
        } catch (Exception $e) {
            http_response_code(500);
            echo json_encode(['error' => $e->getMessage()], JSON_UNESCAPED_UNICODE);
        }
    }

    // Lấy thông tin danh mục theo ID (public - không cần auth)
    public function show($id)
    {
        $this->cleanOutput();
        header('Content-Type: application/json; charset=utf-8');

        try {
            $category = $this->categoryModel->getCategoryById($id);

            if ($category) {
                echo json_encode($category, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
            } else {
                http_response_code(404);
                echo json_encode(['message' => 'Category not found'], JSON_UNESCAPED_UNICODE);
            }
        } catch (Exception $e) {
            http_response_code(500);
            echo json_encode(['error' => $e->getMessage()], JSON_UNESCAPED_UNICODE);
        }
    }

    // Thêm danh mục mới (chỉ admin)
    public function store()
    {
        $this->cleanOutput();
        header('Content-Type: application/json; charset=utf-8');

        // Xác thực JWT token
        $userData = $this->jwtMiddleware->authenticate();
        if (!$userData) {
            $this->jwtMiddleware->unauthorizedResponse('Token không hợp lệ hoặc đã hết hạn');
        }

        // Kiểm tra quyền admin
        if (!$this->jwtMiddleware->isAdmin($userData)) {
            $this->jwtMiddleware->forbiddenResponse('Chỉ admin mới có quyền thêm danh mục');
        }

        try {
            $data = json_decode(file_get_contents("php://input"), true);

            if (json_last_error() !== JSON_ERROR_NONE) {
                throw new Exception('Invalid JSON: ' . json_last_error_msg());
            }

            $name = $data['name'] ?? '';
            $description = $data['description'] ?? '';

            $result = $this->categoryModel->addCategory($name, $description);

            if (is_array($result)) {
                http_response_code(400);
                echo json_encode(['errors' => $result], JSON_UNESCAPED_UNICODE);
            } else {
                http_response_code(201);
                echo json_encode(['message' => 'Category created successfully'], JSON_UNESCAPED_UNICODE);
            }
        } catch (Exception $e) {
            http_response_code(500);
            echo json_encode(['error' => $e->getMessage()], JSON_UNESCAPED_UNICODE);
        }
    }

    // Cập nhật danh mục theo ID (chỉ admin)
    public function update($id)
    {
        $this->cleanOutput();
        header('Content-Type: application/json; charset=utf-8');

        // Xác thực JWT token
        $userData = $this->jwtMiddleware->authenticate();
        if (!$userData) {
            $this->jwtMiddleware->unauthorizedResponse('Token không hợp lệ hoặc đã hết hạn');
        }

        // Kiểm tra quyền admin
        if (!$this->jwtMiddleware->isAdmin($userData)) {
            $this->jwtMiddleware->forbiddenResponse('Chỉ admin mới có quyền cập nhật danh mục');
        }

        try {
            $data = json_decode(file_get_contents("php://input"), true);

            if (json_last_error() !== JSON_ERROR_NONE) {
                throw new Exception('Invalid JSON: ' . json_last_error_msg());
            }

            $name = $data['name'] ?? '';
            $description = $data['description'] ?? '';

            $result = $this->categoryModel->updateCategory($id, $name, $description);

            if (is_array($result)) {
                http_response_code(400);
                echo json_encode(['errors' => $result], JSON_UNESCAPED_UNICODE);
            } elseif ($result) {
                echo json_encode(['message' => 'Category updated successfully'], JSON_UNESCAPED_UNICODE);
            } else {
                http_response_code(400);
                echo json_encode(['message' => 'Category update failed'], JSON_UNESCAPED_UNICODE);
            }
        } catch (Exception $e) {
            http_response_code(500);
            echo json_encode(['error' => $e->getMessage()], JSON_UNESCAPED_UNICODE);
        }
    }

    // Xóa danh mục theo ID (chỉ admin)
    public function destroy($id)
    {
        $this->cleanOutput();
        header('Content-Type: application/json; charset=utf-8');

        // Xác thực JWT token
        $userData = $this->jwtMiddleware->authenticate();
        if (!$userData) {
            $this->jwtMiddleware->unauthorizedResponse('Token không hợp lệ hoặc đã hết hạn');
        }

        // Kiểm tra quyền admin
        if (!$this->jwtMiddleware->isAdmin($userData)) {
            $this->jwtMiddleware->forbiddenResponse('Chỉ admin mới có quyền xóa danh mục');
        }

        try {
            $result = $this->categoryModel->deleteCategory($id);

            if (is_array($result)) {
                http_response_code(400);
                echo json_encode($result, JSON_UNESCAPED_UNICODE);
            } elseif ($result) {
                echo json_encode(['message' => 'Category deleted successfully'], JSON_UNESCAPED_UNICODE);
            } else {
                http_response_code(400);
                echo json_encode(['message' => 'Category deletion failed'], JSON_UNESCAPED_UNICODE);
            }
        } catch (Exception $e) {
            http_response_code(500);
            echo json_encode(['error' => $e->getMessage()], JSON_UNESCAPED_UNICODE);
        }
    }
}
?>