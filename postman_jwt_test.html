<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Postman JWT Test Guide</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-4">
        <h1>🚀 JWT Authentication Test Guide</h1>
        <p class="lead">Hướng dẫn test JWT authentication với Postman</p>
        
        <!-- Authentication Rules -->
        <div class="card mb-4">
            <div class="card-header">
                <h5>🔐 Authentication Rules</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6 class="text-success">✅ PUBLIC (No JWT needed)</h6>
                        <ul>
                            <li><code>GET /webbanhang/api/product</code> - <PERSON>em danh sách sản phẩm</li>
                            <li><code>GET /webbanhang/api/product/{id}</code> - Xem chi tiết sản phẩm</li>
                            <li><code>GET /webbanhang/api/category</code> - Xem danh sách danh mục</li>
                            <li><code>GET /webbanhang/api/category/{id}</code> - Xem chi tiết danh mục</li>
                            <li><code>POST /webbanhang/account/checkLogin</code> - Đăng nhập</li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h6 class="text-danger">🔒 PROTECTED (JWT + Admin required)</h6>
                        <ul>
                            <li><code>POST /webbanhang/api/product</code> - Thêm sản phẩm</li>
                            <li><code>PUT /webbanhang/api/product/{id}</code> - Sửa sản phẩm</li>
                            <li><code>DELETE /webbanhang/api/product/{id}</code> - Xóa sản phẩm</li>
                            <li><code>POST /webbanhang/api/category</code> - Thêm danh mục</li>
                            <li><code>PUT /webbanhang/api/category/{id}</code> - Sửa danh mục</li>
                            <li><code>DELETE /webbanhang/api/category/{id}</code> - Xóa danh mục</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>

        <!-- Step by Step Guide -->
        <div class="card mb-4">
            <div class="card-header">
                <h5>📋 Step by Step Guide</h5>
            </div>
            <div class="card-body">
                <h6>Step 1: Login to get JWT Token</h6>
                <div class="bg-light p-3 mb-3">
                    <strong>POST</strong> <code>http://localhost/webbanhang/account/checkLogin</code><br>
                    <strong>Headers:</strong> <code>Content-Type: application/json</code><br>
                    <strong>Body (JSON):</strong>
                    <pre>{
  "username": "admin",
  "password": "admin123"
}</pre>
                    <strong>Expected Response:</strong>
                    <pre>{
  "token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9..."
}</pre>
                </div>

                <h6>Step 2: Test PUBLIC endpoints (No token needed)</h6>
                <div class="bg-light p-3 mb-3">
                    <strong>GET</strong> <code>http://localhost/webbanhang/api/product</code><br>
                    <strong>Headers:</strong> None required<br>
                    <strong>Expected:</strong> ✅ 200 OK with product list
                </div>

                <h6>Step 3: Test PROTECTED endpoints without token</h6>
                <div class="bg-light p-3 mb-3">
                    <strong>POST</strong> <code>http://localhost/webbanhang/api/product</code><br>
                    <strong>Headers:</strong> <code>Content-Type: application/json</code><br>
                    <strong>Body:</strong> Any product data<br>
                    <strong>Expected:</strong> ❌ 401 Unauthorized
                </div>

                <h6>Step 4: Test PROTECTED endpoints with valid token</h6>
                <div class="bg-light p-3 mb-3">
                    <strong>POST</strong> <code>http://localhost/webbanhang/api/product</code><br>
                    <strong>Headers:</strong><br>
                    <code>Content-Type: application/json</code><br>
                    <code>Authorization: Bearer YOUR_JWT_TOKEN_HERE</code><br>
                    <strong>Body (JSON):</strong>
                    <pre>{
  "name": "Test Product from Postman",
  "description": "Created via API",
  "price": -500,
  "category_id": 1
}</pre>
                    <strong>Expected:</strong> ✅ 201 Created
                </div>
            </div>
        </div>

        <!-- Quick Test Tool -->
        <div class="card mb-4">
            <div class="card-header">
                <h5>🧪 Quick Test Tool</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6>Get JWT Token</h6>
                        <button class="btn btn-primary w-100 mb-2" onclick="getJWTToken()">Login & Get Token</button>
                        <div id="tokenResult" class="mt-2"></div>
                    </div>
                    <div class="col-md-6">
                        <h6>Test API Calls</h6>
                        <button class="btn btn-success w-100 mb-2" onclick="testPublicAPI()">Test Public API</button>
                        <button class="btn btn-warning w-100 mb-2" onclick="testProtectedAPINoToken()">Test Protected (No Token)</button>
                        <button class="btn btn-info w-100 mb-2" onclick="testProtectedAPIWithToken()">Test Protected (With Token)</button>
                    </div>
                </div>
                <div id="testResult" class="mt-3"></div>
            </div>
        </div>

        <!-- Postman Collection -->
        <div class="card">
            <div class="card-header">
                <h5>📦 Postman Collection</h5>
            </div>
            <div class="card-body">
                <p>Copy these requests to your Postman:</p>
                
                <h6>1. Login</h6>
                <div class="bg-light p-2 mb-2">
                    <small>POST http://localhost/webbanhang/account/checkLogin</small>
                </div>

                <h6>2. Get Products (Public)</h6>
                <div class="bg-light p-2 mb-2">
                    <small>GET http://localhost/webbanhang/api/product</small>
                </div>

                <h6>3. Create Product (Protected)</h6>
                <div class="bg-light p-2 mb-2">
                    <small>POST http://localhost/webbanhang/api/product</small><br>
                    <small>Authorization: Bearer {{token}}</small>
                </div>

                <h6>4. Update Product (Protected)</h6>
                <div class="bg-light p-2 mb-2">
                    <small>PUT http://localhost/webbanhang/api/product/1</small><br>
                    <small>Authorization: Bearer {{token}}</small>
                </div>

                <h6>5. Delete Product (Protected)</h6>
                <div class="bg-light p-2 mb-2">
                    <small>DELETE http://localhost/webbanhang/api/product/1</small><br>
                    <small>Authorization: Bearer {{token}}</small>
                </div>
            </div>
        </div>
    </div>

    <script>
        let currentToken = '';

        async function getJWTToken() {
            try {
                const response = await fetch('/webbanhang/account/checkLogin', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        username: 'admin',
                        password: 'admin123'
                    })
                });

                const data = await response.json();
                
                if (data.token) {
                    currentToken = data.token;
                    document.getElementById('tokenResult').innerHTML = 
                        `<div class="alert alert-success">
                            <strong>✅ Token received!</strong><br>
                            <small>${data.token.substring(0, 50)}...</small><br>
                            <button class="btn btn-sm btn-outline-primary mt-1" onclick="copyToken()">Copy Token</button>
                        </div>`;
                } else {
                    document.getElementById('tokenResult').innerHTML = 
                        `<div class="alert alert-danger">❌ Login failed</div>`;
                }
            } catch (error) {
                document.getElementById('tokenResult').innerHTML = 
                    `<div class="alert alert-danger">❌ Error: ${error.message}</div>`;
            }
        }

        async function testPublicAPI() {
            try {
                const response = await fetch('/webbanhang/api/product');
                const data = await response.json();
                
                showTestResult('Public API (GET Products)', response.status, data);
            } catch (error) {
                showTestResult('Public API', 'ERROR', { error: error.message });
            }
        }

        async function testProtectedAPINoToken() {
            try {
                const response = await fetch('/webbanhang/api/product', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        name: 'Test Product',
                        description: 'Should fail',
                        price: 100,
                        category_id: 1
                    })
                });

                const data = await response.json();
                showTestResult('Protected API (No Token)', response.status, data);
            } catch (error) {
                showTestResult('Protected API (No Token)', 'ERROR', { error: error.message });
            }
        }

        async function testProtectedAPIWithToken() {
            if (!currentToken) {
                showTestResult('Protected API (With Token)', 'ERROR', { error: 'No token available. Please login first.' });
                return;
            }

            try {
                const response = await fetch('/webbanhang/api/product', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${currentToken}`
                    },
                    body: JSON.stringify({
                        name: 'Test Product with JWT',
                        description: 'Created with valid token',
                        price: -999,
                        category_id: 1
                    })
                });

                const data = await response.json();
                showTestResult('Protected API (With Token)', response.status, data);
            } catch (error) {
                showTestResult('Protected API (With Token)', 'ERROR', { error: error.message });
            }
        }

        function copyToken() {
            navigator.clipboard.writeText(currentToken);
            alert('Token copied to clipboard!');
        }

        function showTestResult(operation, status, data) {
            const statusClass = (status >= 200 && status < 300) ? 'success' : 'danger';
            document.getElementById('testResult').innerHTML = 
                `<div class="alert alert-${statusClass}">
                    <strong>${operation}</strong><br>
                    Status: ${status}<br>
                    <details>
                        <summary>Response</summary>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    </details>
                </div>`;
        }
    </script>
</body>
</html>
