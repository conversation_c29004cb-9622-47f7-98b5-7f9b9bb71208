<?php

require_once('app/config/database.php');
require_once('app/models/CategoryModel.php');
require_once('app/helpers/SessionHelper.php');
require_once('app/middleware/JWTMiddleware.php');

class CategoryController
{
    private $categoryModel;
    private $db;
    private $jwtMiddleware;

    public function __construct()
    {
        $this->db = (new Database())->getConnection();
        $this->categoryModel = new CategoryModel($this->db);
        $this->jwtMiddleware = new JWTMiddleware();
    }

    // Kiểm tra quyền Admin (chỉ JWT)
    private function isAdmin()
    {
        try {
            $userData = $this->jwtMiddleware->authenticate();
            return $userData && $this->jwtMiddleware->isAdmin($userData);
        } catch (Exception $e) {
            return false;
        }
    }

    // Hiển thị danh sách danh mục
    public function index()
    {
        $categories = $this->categoryModel->getCategories();
        include 'app/views/category/list.php';
    }

    // Hiển thị form thêm danh mục (chỉ Admin)
    public function add()
    {
        if (!$this->isAdmin()) {
            echo "Bạn không có quyền truy cập chức năng này!";
            exit;
        }
        include_once 'app/views/category/add.php';
    }

    // Lưu danh mục mới (chỉ Admin)
    public function save()
    {
        if (!$this->isAdmin()) {
            echo "Bạn không có quyền truy cập chức năng này!";
            exit;
        }

        if ($_SERVER['REQUEST_METHOD'] == 'POST') {
            $name = $_POST['name'] ?? '';
            $description = $_POST['description'] ?? '';

            $result = $this->categoryModel->addCategory($name, $description);
            if (is_array($result)) {
                $errors = $result;
                include 'app/views/category/add.php';
            } else {
                header('Location: /webbanhang/Category');
            }
        }
    }

    // Hiển thị form sửa danh mục (chỉ Admin)
    public function edit($id)
    {
        if (!$this->isAdmin()) {
            echo "Bạn không có quyền truy cập chức năng này!";
            exit;
        }

        $category = $this->categoryModel->getCategoryById($id);

        if (!$category) {
            echo "Không tìm thấy danh mục!";
            exit;
        }

        include 'app/views/category/edit.php';
    }

    // Cập nhật danh mục (chỉ Admin)
    public function update()
    {
        if (!$this->isAdmin()) {
            echo "Bạn không có quyền truy cập chức năng này!";
            exit;
        }

        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            $id = $_POST['id'];
            $name = $_POST['name'];
            $description = $_POST['description'];

            $result = $this->categoryModel->updateCategory($id, $name, $description);
            if (is_array($result)) {
                $errors = $result;
                $category = $this->categoryModel->getCategoryById($id);
                include 'app/views/category/edit.php';
            } else {
                header('Location: /webbanhang/Category');
            }
        }
    }

    // Xóa danh mục (chỉ Admin)
    public function delete($id)
    {
        if (!$this->isAdmin()) {
            echo "Bạn không có quyền truy cập chức năng này!";
            exit;
        }

        $result = $this->categoryModel->deleteCategory($id);
        if (is_array($result)) {
            echo $result['error'];
        } else {
            header('Location: /webbanhang/Category');
        }
    }
}
?>