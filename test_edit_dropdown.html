<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Edit Dropdown</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-4">
        <h1>🔍 Test Edit Product Dropdown</h1>
        
        <!-- Quick Login -->
        <div class="card mb-4">
            <div class="card-header">
                <h5>🔑 Quick Login</h5>
            </div>
            <div class="card-body">
                <button class="btn btn-primary" onclick="quickLogin('admin', 'admin123')">Login as Admin</button>
                <div id="loginResult" class="mt-2"></div>
            </div>
        </div>

        <!-- Comparison Test -->
        <div class="card mb-4">
            <div class="card-header">
                <h5>📊 Comparison Test</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6>Add Product (Working)</h6>
                        <a href="/webbanhang/Product/add" class="btn btn-success w-100 mb-2" target="_blank">Open Add Product</a>
                        <button class="btn btn-outline-info w-100 mb-2" onclick="testAddPageCategories()">Test Add Page Categories</button>
                    </div>
                    <div class="col-md-6">
                        <h6>Edit Product (Fixed)</h6>
                        <a href="/webbanhang/Product/edit/1" class="btn btn-warning w-100 mb-2" target="_blank">Open Edit Product 1</a>
                        <button class="btn btn-outline-warning w-100 mb-2" onclick="testEditPageCategories()">Test Edit Page Categories</button>
                    </div>
                </div>
                <div id="testResult" class="mt-3"></div>
            </div>
        </div>

        <!-- Manual Dropdown Test -->
        <div class="card mb-4">
            <div class="card-header">
                <h5>🧪 Manual Dropdown Test</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6>Add Style Dropdown (form-select)</h6>
                        <select class="form-select mb-2" id="add-style-dropdown">
                            <option value="">-- Chọn danh mục --</option>
                        </select>
                        <button class="btn btn-success" onclick="populateDropdown('add-style-dropdown')">Populate</button>
                    </div>
                    <div class="col-md-6">
                        <h6>Edit Style Dropdown (form-select)</h6>
                        <select class="form-select mb-2" id="edit-style-dropdown">
                            <option value="">-- Chọn danh mục --</option>
                        </select>
                        <button class="btn btn-warning" onclick="populateDropdown('edit-style-dropdown')">Populate</button>
                    </div>
                </div>
                <div id="dropdownResult" class="mt-3"></div>
            </div>
        </div>

        <!-- Debug Info -->
        <div class="card">
            <div class="card-header">
                <h5>🔧 Debug Info</h5>
            </div>
            <div class="card-body">
                <button class="btn btn-info me-2" onclick="testCategoryAPI()">Test Category API</button>
                <button class="btn btn-secondary me-2" onclick="testProductAPI()">Test Product API</button>
                <button class="btn btn-outline-primary" onclick="checkBrowserConsole()">Check Console</button>
                <div id="debugResult" class="mt-3"></div>
            </div>
        </div>
    </div>

    <script>
        // Quick login
        async function quickLogin(username, password) {
            try {
                const response = await fetch('/webbanhang/account/checkLogin', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ username, password })
                });

                const data = await response.json();
                
                if (data.token) {
                    localStorage.setItem('jwtToken', data.token);
                    document.getElementById('loginResult').innerHTML = 
                        `<div class="alert alert-success">✅ Logged in as ${username}</div>`;
                } else {
                    document.getElementById('loginResult').innerHTML = 
                        `<div class="alert alert-danger">❌ Login failed</div>`;
                }
            } catch (error) {
                document.getElementById('loginResult').innerHTML = 
                    `<div class="alert alert-danger">❌ Error: ${error.message}</div>`;
            }
        }

        // Test add page categories (simulate add.php behavior)
        async function testAddPageCategories() {
            try {
                const response = await fetch('/webbanhang/api/category');
                const categories = await response.json();
                
                showResult('Add Page Categories', 'SUCCESS', {
                    message: `Found ${categories.length} categories (no auth needed)`,
                    categories: categories.slice(0, 3) // Show first 3
                });
            } catch (error) {
                showResult('Add Page Categories', 'ERROR', { error: error.message });
            }
        }

        // Test edit page categories (simulate edit.php behavior)
        async function testEditPageCategories() {
            const token = localStorage.getItem('jwtToken');
            try {
                // Test both with and without auth
                const responseNoAuth = await fetch('/webbanhang/api/category');
                const categoriesNoAuth = await responseNoAuth.json();
                
                const responseWithAuth = await fetch('/webbanhang/api/category', {
                    headers: { 'Authorization': `Bearer ${token}` }
                });
                const categoriesWithAuth = await responseWithAuth.json();
                
                showResult('Edit Page Categories', 'SUCCESS', {
                    message: `No Auth: ${categoriesNoAuth.length} categories, With Auth: ${categoriesWithAuth.length} categories`,
                    noAuth: categoriesNoAuth.slice(0, 2),
                    withAuth: categoriesWithAuth.slice(0, 2)
                });
            } catch (error) {
                showResult('Edit Page Categories', 'ERROR', { error: error.message });
            }
        }

        // Populate dropdown manually
        async function populateDropdown(dropdownId) {
            const dropdown = document.getElementById(dropdownId);
            const resultDiv = document.getElementById('dropdownResult');
            
            try {
                // Clear existing options except first
                while (dropdown.children.length > 1) {
                    dropdown.removeChild(dropdown.lastChild);
                }

                const response = await fetch('/webbanhang/api/category');
                const categories = await response.json();
                
                if (Array.isArray(categories)) {
                    categories.forEach(category => {
                        const option = document.createElement('option');
                        option.value = category.id;
                        option.textContent = category.name;
                        dropdown.appendChild(option);
                    });
                    
                    resultDiv.innerHTML = 
                        `<div class="alert alert-success">✅ Populated ${dropdownId} with ${categories.length} categories</div>`;
                } else {
                    resultDiv.innerHTML = 
                        `<div class="alert alert-danger">❌ Categories is not an array</div>`;
                }
            } catch (error) {
                resultDiv.innerHTML = 
                    `<div class="alert alert-danger">❌ Error: ${error.message}</div>`;
            }
        }

        // Test category API
        async function testCategoryAPI() {
            try {
                const response = await fetch('/webbanhang/api/category');
                const data = await response.json();
                
                showDebugResult('Category API', response.status, {
                    isArray: Array.isArray(data),
                    length: data.length || 'N/A',
                    firstItem: data[0] || 'N/A',
                    data: data
                });
            } catch (error) {
                showDebugResult('Category API', 'ERROR', { error: error.message });
            }
        }

        // Test product API
        async function testProductAPI() {
            const token = localStorage.getItem('jwtToken');
            try {
                const headers = {};
                if (token) {
                    headers['Authorization'] = `Bearer ${token}`;
                }

                const response = await fetch('/webbanhang/api/product/1', { headers });
                const data = await response.json();
                
                showDebugResult('Product API', response.status, data);
            } catch (error) {
                showDebugResult('Product API', 'ERROR', { error: error.message });
            }
        }

        // Check browser console
        function checkBrowserConsole() {
            document.getElementById('debugResult').innerHTML = 
                `<div class="alert alert-info">
                    <strong>Check Browser Console:</strong><br>
                    1. Press F12 to open Developer Tools<br>
                    2. Go to Console tab<br>
                    3. Look for any JavaScript errors<br>
                    4. Try opening /webbanhang/Product/edit/1 and check console
                </div>`;
        }

        // Helper functions
        function showResult(operation, status, data) {
            const statusClass = status === 'SUCCESS' ? 'success' : 'danger';
            document.getElementById('testResult').innerHTML = 
                `<div class="alert alert-${statusClass}">
                    <strong>${operation}</strong><br>
                    Status: ${status}<br>
                    <details>
                        <summary>Data</summary>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    </details>
                </div>`;
        }

        function showDebugResult(operation, status, data) {
            const statusClass = (status >= 200 && status < 300) ? 'success' : 'danger';
            document.getElementById('debugResult').innerHTML = 
                `<div class="alert alert-${statusClass}">
                    <strong>${operation}</strong><br>
                    Status: ${status}<br>
                    <details>
                        <summary>Response</summary>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    </details>
                </div>`;
        }
    </script>
</body>
</html>
