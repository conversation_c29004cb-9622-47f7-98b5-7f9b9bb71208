<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Login Methods</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <h1>Test Login Methods</h1>
        
        <div class="row">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>Web Login (Session)</h5>
                    </div>
                    <div class="card-body">
                        <form action="/webbanhang/account/processLogin" method="POST">
                            <div class="mb-3">
                                <label for="web_username" class="form-label">Username</label>
                                <input type="text" class="form-control" name="username" id="web_username" value="admin" required>
                            </div>
                            <div class="mb-3">
                                <label for="web_password" class="form-label">Password</label>
                                <input type="password" class="form-control" name="password" id="web_password" value="admin123" required>
                            </div>
                            <button type="submit" class="btn btn-primary">Login with Session</button>
                        </form>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>API Login (JWT)</h5>
                    </div>
                    <div class="card-body">
                        <form id="apiLoginForm">
                            <div class="mb-3">
                                <label for="api_username" class="form-label">Username</label>
                                <input type="text" class="form-control" id="api_username" value="admin" required>
                            </div>
                            <div class="mb-3">
                                <label for="api_password" class="form-label">Password</label>
                                <input type="password" class="form-control" id="api_password" value="admin123" required>
                            </div>
                            <button type="submit" class="btn btn-success">Login with JWT</button>
                        </form>
                        <div id="apiResult" class="mt-3"></div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5>Test Links</h5>
                    </div>
                    <div class="card-body">
                        <a href="/webbanhang/Product/add" class="btn btn-warning me-2">Test Add Product (Web)</a>
                        <a href="/webbanhang/Product" class="btn btn-info me-2">Product List</a>
                        <button class="btn btn-danger" onclick="testAPICall()">Test API Call</button>
                        <button class="btn btn-secondary" onclick="clearStorage()">Clear JWT</button>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5>Current Status</h5>
                    </div>
                    <div class="card-body">
                        <div id="statusInfo"></div>
                        <button class="btn btn-outline-primary" onclick="checkStatus()">Check Status</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // API Login
        document.getElementById('apiLoginForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const username = document.getElementById('api_username').value;
            const password = document.getElementById('api_password').value;
            
            fetch('/webbanhang/account/checkLogin', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ username, password })
            })
            .then(response => response.json())
            .then(data => {
                if (data.token) {
                    localStorage.setItem('jwtToken', data.token);
                    document.getElementById('apiResult').innerHTML = 
                        '<div class="alert alert-success">JWT Login successful!</div>';
                    checkStatus();
                } else {
                    document.getElementById('apiResult').innerHTML = 
                        '<div class="alert alert-danger">JWT Login failed!</div>';
                }
            })
            .catch(error => {
                document.getElementById('apiResult').innerHTML = 
                    '<div class="alert alert-danger">Error: ' + error.message + '</div>';
            });
        });

        // Test API Call
        async function testAPICall() {
            const token = localStorage.getItem('jwtToken');
            try {
                const response = await fetch('/webbanhang/api/product', {
                    headers: {
                        'Authorization': `Bearer ${token}`
                    }
                });
                const data = await response.json();
                alert(`API Call Result: ${response.status} - ${JSON.stringify(data).substring(0, 100)}...`);
            } catch (error) {
                alert('API Call Error: ' + error.message);
            }
        }

        // Clear JWT
        function clearStorage() {
            localStorage.removeItem('jwtToken');
            checkStatus();
        }

        // Check current status
        function checkStatus() {
            const token = localStorage.getItem('jwtToken');
            let statusHTML = '<h6>JWT Token:</h6>';
            
            if (token) {
                try {
                    const base64Url = token.split('.')[1];
                    const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');
                    const jsonPayload = decodeURIComponent(atob(base64).split('').map(function(c) {
                        return '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2);
                    }).join(''));
                    const decoded = JSON.parse(jsonPayload);
                    
                    statusHTML += `<pre>${JSON.stringify(decoded, null, 2)}</pre>`;
                } catch (error) {
                    statusHTML += '<div class="alert alert-warning">Invalid token</div>';
                }
            } else {
                statusHTML += '<div class="alert alert-info">No JWT token found</div>';
            }
            
            document.getElementById('statusInfo').innerHTML = statusHTML;
        }

        // Check status on page load
        checkStatus();
    </script>
</body>
</html>
