<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dual Authentication Test</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-4">
        <h1>🔐 Dual Authentication Test</h1>
        <p class="lead">Test cả Session Authentication (Web) và JWT Authentication (API)</p>
        
        <!-- Login Methods -->
        <div class="card mb-4">
            <div class="card-header">
                <h5>🔑 Login Methods</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6>Session Login (Web)</h6>
                        <p class="text-muted">Tạo PHP Session cho web interface</p>
                        <button class="btn btn-primary w-100 mb-2" onclick="sessionLogin('admin', 'admin123')">Session Login as Admin</button>
                        <button class="btn btn-secondary w-100 mb-2" onclick="sessionLogin('user', 'admin123')">Session Login as User</button>
                    </div>
                    <div class="col-md-6">
                        <h6>JWT Login (API)</h6>
                        <p class="text-muted">Tạo JWT Token cho API calls</p>
                        <button class="btn btn-success w-100 mb-2" onclick="jwtLogin('admin', 'admin123')">JWT Login as Admin</button>
                        <button class="btn btn-info w-100 mb-2" onclick="jwtLogin('user', 'admin123')">JWT Login as User</button>
                    </div>
                </div>
                <div class="row mt-3">
                    <div class="col-md-6">
                        <a href="/webbanhang/account/logout" class="btn btn-outline-danger w-100">Session Logout</a>
                    </div>
                    <div class="col-md-6">
                        <button class="btn btn-outline-warning w-100" onclick="clearJWT()">Clear JWT</button>
                    </div>
                </div>
                <div id="loginResult" class="mt-3"></div>
            </div>
        </div>

        <!-- Test Access -->
        <div class="card mb-4">
            <div class="card-header">
                <h5>🧪 Test Access</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6>Web Interface (Session)</h6>
                        <a href="/webbanhang/Product" class="btn btn-outline-primary w-100 mb-2" target="_blank">Product List</a>
                        <a href="/webbanhang/Product/add" class="btn btn-outline-success w-100 mb-2" target="_blank">Add Product (Admin)</a>
                        <a href="/webbanhang/Category" class="btn btn-outline-primary w-100 mb-2" target="_blank">Category List</a>
                        <a href="/webbanhang/Category/add" class="btn btn-outline-success w-100 mb-2" target="_blank">Add Category (Admin)</a>
                    </div>
                    <div class="col-md-6">
                        <h6>API Calls (JWT)</h6>
                        <button class="btn btn-outline-info w-100 mb-2" onclick="testAPI('GET', '/api/product')">GET Products</button>
                        <button class="btn btn-outline-success w-100 mb-2" onclick="testCreateProduct()">POST Product (Admin)</button>
                        <button class="btn btn-outline-info w-100 mb-2" onclick="testAPI('GET', '/api/category')">GET Categories</button>
                        <button class="btn btn-outline-success w-100 mb-2" onclick="testCreateCategory()">POST Category (Admin)</button>
                    </div>
                </div>
                <div id="testResult" class="mt-3"></div>
            </div>
        </div>

        <!-- Expected Results -->
        <div class="card mb-4">
            <div class="card-header">
                <h5>📋 Expected Results</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6 class="text-success">✅ Admin (Both Session & JWT)</h6>
                        <ul class="list-group list-group-flush">
                            <li class="list-group-item">Can access /Product/add and /Category/add</li>
                            <li class="list-group-item">Can call POST API endpoints</li>
                            <li class="list-group-item">See all admin buttons in UI</li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h6 class="text-warning">⚠️ User (Both Session & JWT)</h6>
                        <ul class="list-group list-group-flush">
                            <li class="list-group-item">Cannot access /Product/add and /Category/add</li>
                            <li class="list-group-item">Cannot call POST API endpoints (403)</li>
                            <li class="list-group-item">No admin buttons in UI</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>

        <!-- Current Status -->
        <div class="card">
            <div class="card-header">
                <h5>📊 Current Status</h5>
            </div>
            <div class="card-body">
                <div id="statusInfo"></div>
                <button class="btn btn-outline-primary" onclick="checkStatus()">Refresh Status</button>
            </div>
        </div>
    </div>

    <script>
        // Session Login (Web)
        function sessionLogin(username, password) {
            const form = document.createElement('form');
            form.method = 'POST';
            form.action = '/webbanhang/account/processLogin';
            
            const usernameInput = document.createElement('input');
            usernameInput.type = 'hidden';
            usernameInput.name = 'username';
            usernameInput.value = username;
            
            const passwordInput = document.createElement('input');
            passwordInput.type = 'hidden';
            passwordInput.name = 'password';
            passwordInput.value = password;
            
            form.appendChild(usernameInput);
            form.appendChild(passwordInput);
            document.body.appendChild(form);
            form.submit();
        }

        // JWT Login (API)
        async function jwtLogin(username, password) {
            try {
                const response = await fetch('/webbanhang/account/checkLogin', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ username, password })
                });

                const data = await response.json();
                
                if (data.token) {
                    localStorage.setItem('jwtToken', data.token);
                    document.getElementById('loginResult').innerHTML = 
                        `<div class="alert alert-success">✅ JWT Login successful as ${username}</div>`;
                    checkStatus();
                } else {
                    document.getElementById('loginResult').innerHTML = 
                        `<div class="alert alert-danger">❌ JWT Login failed</div>`;
                }
            } catch (error) {
                document.getElementById('loginResult').innerHTML = 
                    `<div class="alert alert-danger">❌ Error: ${error.message}</div>`;
            }
        }

        // Clear JWT
        function clearJWT() {
            localStorage.removeItem('jwtToken');
            document.getElementById('loginResult').innerHTML = 
                '<div class="alert alert-info">JWT Token cleared</div>';
            checkStatus();
        }

        // Test API
        async function testAPI(method, endpoint) {
            const token = localStorage.getItem('jwtToken');
            try {
                const headers = { 'Content-Type': 'application/json' };
                if (token) {
                    headers['Authorization'] = `Bearer ${token}`;
                }

                const response = await fetch(`/webbanhang${endpoint}`, {
                    method: method,
                    headers: headers
                });

                const data = await response.json();
                const statusClass = response.ok ? 'success' : 'danger';
                
                document.getElementById('testResult').innerHTML = 
                    `<div class="alert alert-${statusClass}">
                        <strong>${method} ${endpoint}</strong><br>
                        Status: ${response.status}<br>
                        Response: ${JSON.stringify(data).substring(0, 100)}...
                    </div>`;
            } catch (error) {
                document.getElementById('testResult').innerHTML = 
                    `<div class="alert alert-danger">❌ Error: ${error.message}</div>`;
            }
        }

        // Test create product
        async function testCreateProduct() {
            const token = localStorage.getItem('jwtToken');
            try {
                const response = await fetch('/webbanhang/api/product', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${token}`
                    },
                    body: JSON.stringify({
                        name: 'Test Product ' + Date.now(),
                        description: 'Created via dual auth test',
                        price: 99000,
                        category_id: 1
                    })
                });

                const data = await response.json();
                const statusClass = response.ok ? 'success' : 'danger';
                
                document.getElementById('testResult').innerHTML = 
                    `<div class="alert alert-${statusClass}">
                        <strong>POST /api/product</strong><br>
                        Status: ${response.status}<br>
                        Response: ${JSON.stringify(data)}
                    </div>`;
            } catch (error) {
                document.getElementById('testResult').innerHTML = 
                    `<div class="alert alert-danger">❌ Error: ${error.message}</div>`;
            }
        }

        // Test create category
        async function testCreateCategory() {
            const token = localStorage.getItem('jwtToken');
            try {
                const response = await fetch('/webbanhang/api/category', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${token}`
                    },
                    body: JSON.stringify({
                        name: 'Test Category ' + Date.now(),
                        description: 'Created via dual auth test'
                    })
                });

                const data = await response.json();
                const statusClass = response.ok ? 'success' : 'danger';
                
                document.getElementById('testResult').innerHTML = 
                    `<div class="alert alert-${statusClass}">
                        <strong>POST /api/category</strong><br>
                        Status: ${response.status}<br>
                        Response: ${JSON.stringify(data)}
                    </div>`;
            } catch (error) {
                document.getElementById('testResult').innerHTML = 
                    `<div class="alert alert-danger">❌ Error: ${error.message}</div>`;
            }
        }

        // Decode JWT
        function decodeJWT(token) {
            try {
                const base64Url = token.split('.')[1];
                const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');
                const jsonPayload = decodeURIComponent(atob(base64).split('').map(function(c) {
                    return '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2);
                }).join(''));
                return JSON.parse(jsonPayload);
            } catch (error) {
                return null;
            }
        }

        // Check status
        function checkStatus() {
            const token = localStorage.getItem('jwtToken');
            let statusHTML = '<h6>Authentication Status:</h6>';
            
            // JWT Status
            if (token) {
                const decoded = decodeJWT(token);
                if (decoded) {
                    statusHTML += `
                        <div class="alert alert-info">
                            <strong>JWT:</strong> ✅ Active<br>
                            <strong>User:</strong> ${decoded.data.username}<br>
                            <strong>Role:</strong> ${decoded.data.role}<br>
                            <strong>Expires:</strong> ${new Date(decoded.exp * 1000).toLocaleString()}
                        </div>`;
                } else {
                    statusHTML += '<div class="alert alert-warning">JWT: ❌ Invalid token</div>';
                }
            } else {
                statusHTML += '<div class="alert alert-secondary">JWT: ❌ No token</div>';
            }
            
            // Session Status (can't check from client-side, but show instruction)
            statusHTML += `
                <div class="alert alert-light">
                    <strong>Session:</strong> Check by accessing web pages<br>
                    <small>If you can access /Product/add without login redirect, session is active</small>
                </div>`;
            
            document.getElementById('statusInfo').innerHTML = statusHTML;
        }

        // Check status on page load
        checkStatus();
    </script>
</body>
</html>
