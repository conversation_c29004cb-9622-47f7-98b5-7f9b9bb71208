
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON>u<PERSON><PERSON> l<PERSON> sản phẩm</title>
    <link href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css" rel="stylesheet">
    <style>
        .product-image {
            max-width: 100px;
            height: auto;
        }
        .navbar {
            padding: 0.5rem 1rem;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .nav-link {
            padding: 0.5rem 1rem;
            transition: all 0.3s ease;
        }
        .nav-link:hover {
            background: rgba(0,0,0,0.05);
        }
    </style>
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-light bg-light">
        <a class="navbar-brand" href="/webbanhang">Qu<PERSON>n lý sản phẩm</a>
        <button class="navbar-toggler" type="button" data-toggle="collapse" data-target="#navbarNav" 
                aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
            <span class="navbar-toggler-icon"></span>
        </button>
        <div class="collapse navbar-collapse" id="navbarNav">
            <ul class="navbar-nav mr-auto">
                <li class="nav-item">
                    <a class="nav-link" href="/webbanhang/Product/">Danh sách sản phẩm</a>
                </li>
                <li class="nav-item admin-only" style="display: none;">
                    <a class="nav-link" href="/webbanhang/Product/add">Thêm sản phẩm</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="/webbanhang/Category/">Danh sách danh mục</a>
                </li>
                <li class="nav-item admin-only" style="display: none;">
                    <a class="nav-link" href="/webbanhang/Category/add">Thêm danh mục</a>
                </li>
            </ul>
            <ul class="navbar-nav">
                <li class="nav-item" id="nav-login">
                    <a class="nav-link" href="/webbanhang/account/login">Login</a>
                </li>
                <li class="nav-item" id="nav-logout" style="display: none;">
                    <a class="nav-link" href="#" onclick="logout()">Logout</a>
                </li>
            </ul>
        </div>
    </nav>

    <script>
    function logout() {
        localStorage.removeItem('jwtToken');
        window.location.href = '/webbanhang/account/login';
    }

    // Function để decode JWT token
    function decodeJWT(token) {
        try {
            const base64Url = token.split('.')[1];
            const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');
            const jsonPayload = decodeURIComponent(atob(base64).split('').map(function(c) {
                return '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2);
            }).join(''));
            return JSON.parse(jsonPayload);
        } catch (error) {
            return null;
        }
    }

    // Function kiểm tra user có phải admin không
    function isAdmin() {
        const token = localStorage.getItem('jwtToken');
        if (!token) return false;

        const decoded = decodeJWT(token);
        return decoded && decoded.data && decoded.data.role === 'admin';
    }

    document.addEventListener("DOMContentLoaded", function() {
        const token = localStorage.getItem('jwtToken');
        const navLogin = document.getElementById('nav-login');
        const navLogout = document.getElementById('nav-logout');
        const adminOnlyItems = document.querySelectorAll('.admin-only');

        if (token) {
            navLogin.style.display = 'none';
            navLogout.style.display = 'block';

            // Hiển thị menu admin nếu là admin
            if (isAdmin()) {
                adminOnlyItems.forEach(item => {
                    item.style.display = 'block';
                });
            }
        } else {
            navLogin.style.display = 'block';
            navLogout.style.display = 'none';

            // Ẩn tất cả menu admin
            adminOnlyItems.forEach(item => {
                item.style.display = 'none';
            });

            // Redirect to login if accessing protected routes
            const protectedRoutes = ['/webbanhang/Product/add', '/webbanhang/Category/add'];
            if (protectedRoutes.includes(window.location.pathname)) {
                window.location.href = '/webbanhang/account/login';
            }
        }
    });
    </script>

    <div class="container mt-4">