<style>
    .login-container {
        min-height: 100vh;
        background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
    }

    .login-brand-side {
        background: linear-gradient(135deg, rgba(99, 102, 241, 0.9) 0%, rgba(79, 70, 229, 0.9) 100%);
        position: relative;
        overflow: hidden;
    }

    .login-brand-side::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
        opacity: 0.3;
    }

    .login-brand-side::after {
        content: '';
        position: absolute;
        top: -50%;
        left: -50%;
        width: 200%;
        height: 200%;
        background: radial-gradient(circle, rgba(255,255,255,0.1) 1px, transparent 1px);
        background-size: 50px 50px;
        animation: float-dots 20s linear infinite;
        opacity: 0.4;
    }

    @keyframes float-dots {
        0% { transform: translate(0, 0) rotate(0deg); }
        100% { transform: translate(-50px, -50px) rotate(360deg); }
    }

    .floating-elements {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        pointer-events: none;
        overflow: hidden;
    }

    .floating-element {
        position: absolute;
        width: 20px;
        height: 20px;
        background: rgba(255, 255, 255, 0.2);
        border-radius: 50%;
        animation: float-up 6s ease-in-out infinite;
    }

    .floating-element:nth-child(2n) {
        width: 15px;
        height: 15px;
        background: rgba(251, 191, 36, 0.3);
    }

    .floating-element:nth-child(3n) {
        width: 25px;
        height: 25px;
        background: rgba(255, 255, 255, 0.1);
        border: 2px solid rgba(255, 255, 255, 0.2);
        background: transparent;
    }

    @keyframes float-up {
        0%, 100% {
            transform: translateY(0) rotate(0deg);
            opacity: 0.7;
        }
        50% {
            transform: translateY(-20px) rotate(180deg);
            opacity: 1;
        }
    }

    .brand-content {
        position: relative;
        z-index: 2;
    }

    .brand-icon {
        font-size: 4rem;
        color: var(--warning-color);
        margin-bottom: 2rem;
    }

    .brand-title {
        font-size: 3rem;
        font-weight: 700;
        margin-bottom: 1rem;
    }

    .brand-subtitle {
        font-size: 1.2rem;
        opacity: 0.9;
        line-height: 1.6;
    }

    .features-list {
        text-align: left;
        max-width: 450px;
    }

    .feature-item {
        display: flex;
        align-items: center;
        margin-bottom: 1.5rem;
        opacity: 0;
        transform: translateX(-20px);
        animation: slideInLeft 0.6s ease forwards;
        background: rgba(255, 255, 255, 0.1);
        padding: 1rem;
        border-radius: 0.75rem;
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.2);
        transition: all 0.3s ease;
    }

    .feature-item:hover {
        background: rgba(255, 255, 255, 0.15);
        transform: translateX(5px);
    }

    .feature-icon-small {
        width: 3rem;
        height: 3rem;
        background: rgba(255, 255, 255, 0.2);
        border-radius: 0.75rem;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 1rem;
        font-size: 1.2rem;
        color: var(--warning-color);
    }

    .feature-title {
        font-weight: 600;
        font-size: 1rem;
        margin-bottom: 0.25rem;
    }

    .feature-desc {
        font-size: 0.9rem;
        opacity: 0.8;
    }

    @keyframes slideInLeft {
        to {
            opacity: 1;
            transform: translateX(0);
        }
    }

    .login-form-side {
        background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
        padding: 3rem 2rem;
        position: relative;
        overflow: hidden;
    }

    .login-form-side::before {
        content: '';
        position: absolute;
        top: 0;
        right: 0;
        width: 200px;
        height: 200px;
        background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
        border-radius: 50%;
        opacity: 0.05;
        transform: translate(50%, -50%);
    }

    .login-form-container {
        width: 100%;
        max-width: 450px;
        position: relative;
        z-index: 2;
    }

    .login-icon {
        font-size: 3rem;
        background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        animation: pulse 2s ease-in-out infinite;
    }

    @keyframes pulse {
        0%, 100% { transform: scale(1); }
        50% { transform: scale(1.05); }
    }

    .login-title {
        font-size: 2.5rem;
        font-weight: 700;
        color: var(--dark-color);
        margin-bottom: 0.5rem;
    }

    .login-subtitle {
        color: var(--text-muted);
        font-size: 1.1rem;
        margin-bottom: 2rem;
    }

    .form-label {
        font-weight: 600;
        color: var(--dark-color);
        margin-bottom: 0.5rem;
    }

    .input-group-text {
        background: var(--light-color);
        border: 1px solid var(--border-color);
        color: var(--text-muted);
    }

    .form-control {
        border: 2px solid var(--border-color);
        padding: 0.875rem 1rem;
        font-size: 1rem;
        transition: all 0.3s ease;
        border-radius: 0.75rem;
        background: rgba(255, 255, 255, 0.8);
        backdrop-filter: blur(10px);
    }

    .form-control:focus {
        border-color: var(--primary-color);
        box-shadow: 0 0 0 0.2rem rgba(99, 102, 241, 0.15);
        background: white;
        transform: translateY(-1px);
    }

    .input-group-text {
        background: linear-gradient(135deg, var(--light-color), #e2e8f0);
        border: 2px solid var(--border-color);
        border-radius: 0.75rem 0 0 0.75rem;
        color: var(--primary-color);
        font-weight: 500;
    }

    .input-group .form-control {
        border-left: none;
        border-radius: 0 0.75rem 0.75rem 0;
    }

    .input-group:focus-within .input-group-text {
        border-color: var(--primary-color);
        background: linear-gradient(135deg, rgba(99, 102, 241, 0.1), rgba(79, 70, 229, 0.1));
    }

    .login-btn {
        background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
        border: none;
        padding: 1rem 2rem;
        font-weight: 600;
        font-size: 1.1rem;
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
        border-radius: 0.75rem;
        box-shadow: 0 4px 15px rgba(99, 102, 241, 0.2);
    }

    .login-btn::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
        transition: left 0.5s;
    }

    .login-btn:hover::before {
        left: 100%;
    }

    .login-btn:hover {
        transform: translateY(-3px);
        box-shadow: 0 12px 35px rgba(99, 102, 241, 0.4);
    }

    .login-btn:active {
        transform: translateY(-1px);
    }

    .forgot-password-link {
        color: var(--primary-color);
        text-decoration: none;
        font-weight: 500;
        transition: all 0.3s ease;
    }

    .forgot-password-link:hover {
        color: var(--primary-dark);
        text-decoration: underline;
    }

    .alert-modern {
        border: none;
        border-radius: 0.75rem;
        padding: 1rem 1.25rem;
        font-weight: 500;
    }

    .register-link {
        transition: all 0.3s ease;
    }

    .register-link:hover {
        text-decoration: underline !important;
    }

    @media (max-width: 991.98px) {
        .login-brand-side {
            display: none !important;
        }

        .login-form-side {
            padding: 2rem 1rem;
        }

        .brand-title {
            font-size: 2rem;
        }

        .login-title {
            font-size: 2rem;
        }
    }
</style>

<div class="login-container">
    <div class="container-fluid vh-100">
        <div class="row h-100">
            <!-- Left Side - Branding -->
            <div class="col-lg-6 d-none d-lg-flex login-brand-side">
                <div class="d-flex flex-column justify-content-center align-items-center text-white p-5">
                    <div class="brand-content text-center">
                        <div class="brand-icon mb-4">
                            <i class="fas fa-store"></i>
                        </div>
                        <h1 class="brand-title mb-4">Web Bán Hàng</h1>
                        <p class="brand-subtitle mb-5">
                            Hệ thống quản lý bán hàng hiện đại với JWT Authentication
                        </p>

                        <!-- Decorative elements -->
                        <div class="floating-elements">
                            <div class="floating-element" style="top: 10%; left: 10%; animation-delay: 0s;"></div>
                            <div class="floating-element" style="top: 20%; right: 15%; animation-delay: 1s;"></div>
                            <div class="floating-element" style="bottom: 30%; left: 20%; animation-delay: 2s;"></div>
                            <div class="floating-element" style="bottom: 15%; right: 10%; animation-delay: 3s;"></div>
                        </div>
                        <div class="features-list">
                            <div class="feature-item" style="animation-delay: 0.1s;">
                                <div class="feature-icon-small">
                                    <i class="fas fa-shield-alt"></i>
                                </div>
                                <div>
                                    <div class="feature-title">Bảo mật cao</div>
                                    <div class="feature-desc">JWT Authentication</div>
                                </div>
                            </div>
                            <div class="feature-item" style="animation-delay: 0.2s;">
                                <div class="feature-icon-small">
                                    <i class="fas fa-api"></i>
                                </div>
                                <div>
                                    <div class="feature-title">API hiện đại</div>
                                    <div class="feature-desc">RESTful Architecture</div>
                                </div>
                            </div>
                            <div class="feature-item" style="animation-delay: 0.3s;">
                                <div class="feature-icon-small">
                                    <i class="fas fa-mobile-alt"></i>
                                </div>
                                <div>
                                    <div class="feature-title">Responsive</div>
                                    <div class="feature-desc">Mọi thiết bị</div>
                                </div>
                            </div>
                            <div class="feature-item" style="animation-delay: 0.4s;">
                                <div class="feature-icon-small">
                                    <i class="fas fa-chart-line"></i>
                                </div>
                                <div>
                                    <div class="feature-title">Quản lý thông minh</div>
                                    <div class="feature-desc">Real-time updates</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Right Side - Login Form -->
            <div class="col-lg-6 d-flex align-items-center justify-content-center login-form-side">
                <div class="login-form-container">
                    <div class="text-center mb-5">
                        <div class="login-icon mb-3">
                            <i class="fas fa-user-circle"></i>
                        </div>
                        <h2 class="login-title">Đăng nhập</h2>
                        <p class="login-subtitle">Chào mừng bạn quay trở lại!</p>
                    </div>

                    <?php if (isset($error)): ?>
                        <div class="alert alert-danger alert-modern">
                            <i class="fas fa-exclamation-circle me-2"></i>
                            <?= htmlspecialchars($error) ?>
                        </div>
                    <?php endif; ?>

                    <form id="login-form" action="/webbanhang/account/checkLogin" method="POST" class="login-form">
                        <div class="form-group mb-4">
                            <label class="form-label">Tên đăng nhập</label>
                            <div class="input-group">
                                <span class="input-group-text">
                                    <i class="fas fa-user"></i>
                                </span>
                                <input type="text" name="username" class="form-control form-control-lg" required
                                       placeholder="Nhập tên đăng nhập" />
                            </div>
                        </div>

                        <div class="form-group mb-4">
                            <label class="form-label">Mật khẩu</label>
                            <div class="input-group">
                                <span class="input-group-text">
                                    <i class="fas fa-lock"></i>
                                </span>
                                <input type="password" name="password" class="form-control form-control-lg" required
                                       placeholder="Nhập mật khẩu" id="password-input" />
                                <button class="btn btn-outline-secondary" type="button" onclick="togglePassword()">
                                    <i class="fas fa-eye" id="password-toggle-icon"></i>
                                </button>
                            </div>
                        </div>

                        <div class="d-flex justify-content-between align-items-center mb-4">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="remember-me">
                                <label class="form-check-label" for="remember-me">
                                    Ghi nhớ đăng nhập
                                </label>
                            </div>
                            <a href="#" class="forgot-password-link">Quên mật khẩu?</a>
                        </div>

                        <button class="btn btn-primary btn-lg w-100 login-btn" type="submit" id="login-button">
                            <span class="btn-text">
                                <i class="fas fa-sign-in-alt me-2"></i>
                                Đăng nhập
                            </span>
                            <span class="btn-loading d-none">
                                <i class="fas fa-spinner fa-spin me-2"></i>
                                Đang đăng nhập...
                            </span>
                        </button>

                        <div class="text-center mt-4">
                            <p class="text-muted mb-0">
                                Chưa có tài khoản?
                                <a href="#" class="text-decoration-none fw-semibold" style="color: var(--primary-color);">
                                    Đăng ký ngay
                                </a>
                            </p>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<?php include 'app/views/shares/footer.php'; ?>

<script>
    // Toggle password visibility
    function togglePassword() {
        const passwordInput = document.getElementById('password-input');
        const toggleIcon = document.getElementById('password-toggle-icon');

        if (passwordInput.type === 'password') {
            passwordInput.type = 'text';
            toggleIcon.classList.remove('fa-eye');
            toggleIcon.classList.add('fa-eye-slash');
        } else {
            passwordInput.type = 'password';
            toggleIcon.classList.remove('fa-eye-slash');
            toggleIcon.classList.add('fa-eye');
        }
    }

    // Auto-focus username field
    document.addEventListener('DOMContentLoaded', function() {
        const usernameField = document.querySelector('input[name="username"]');
        if (usernameField) {
            usernameField.focus();
        }
    });

    // Enhanced login form handler
    document.getElementById('login-form').addEventListener('submit', function(e) {
        e.preventDefault();

        const loginButton = document.getElementById('login-button');
        const btnText = loginButton.querySelector('.btn-text');
        const btnLoading = loginButton.querySelector('.btn-loading');

        // Show loading state
        btnText.classList.add('d-none');
        btnLoading.classList.remove('d-none');
        loginButton.disabled = true;

        const formData = new FormData(this);
        const jsonData = {};

        formData.forEach((value, key) => {
            jsonData[key] = value;
        });

        // Gọi API để lấy JWT token
        fetch('/webbanhang/account/checkLogin', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(jsonData)
        })
        .then(response => response.json())
        .then(data => {
            if (data.token) {
                // Lưu JWT token cho API calls
                localStorage.setItem('jwtToken', data.token);

                // Show success message
                showToast('Đăng nhập thành công! Đang chuyển hướng...', 'success');

                // Smooth redirect
                setTimeout(() => {
                    location.href = '/webbanhang/Product';
                }, 1000);
            } else {
                throw new Error(data.message || 'Đăng nhập thất bại');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showToast('Đăng nhập thất bại: ' + error.message, 'danger');

            // Reset button state
            btnText.classList.remove('d-none');
            btnLoading.classList.add('d-none');
            loginButton.disabled = false;
        });
    });

    // Add floating animation to brand icon
    document.addEventListener('DOMContentLoaded', function() {
        const brandIcon = document.querySelector('.brand-icon i');
        if (brandIcon) {
            brandIcon.style.animation = 'float 3s ease-in-out infinite';
        }

        // Add fade-in animation to form
        const formContainer = document.querySelector('.login-form-container');
        if (formContainer) {
            formContainer.style.opacity = '0';
            formContainer.style.transform = 'translateY(20px)';
            formContainer.style.transition = 'all 0.6s ease';

            setTimeout(() => {
                formContainer.style.opacity = '1';
                formContainer.style.transform = 'translateY(0)';
            }, 200);
        }
    });

    // Add CSS for floating animation
    const style = document.createElement('style');
    style.textContent = `
        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
        }
    `;
    document.head.appendChild(style);
</script>