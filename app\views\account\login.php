<?php include 'app/views/shares/header.php'; ?>

<section class="vh-100 gradient-custom">
    <div class="container py-5 h-100">
        <div class="row d-flex justify-content-center align-items-center h-100">
            <div class="col-12 col-md-8 col-lg-6 col-xl-5">
                <div class="card bg-dark text-white" style="border-radius: 1rem;">
                    <div class="card-body p-5 text-center">
                        <div id="loginMessage" class="mb-3"></div>
                        <?php if (isset($error)): ?>
                            <div class="alert alert-danger"><?= htmlspecialchars($error) ?></div>
                        <?php endif; ?>

                        <div class="mb-md-5 mt-md-4 pb-5">
                            <h2 class="fw-bold mb-2 text-uppercase">Login</h2>
                            <p class="text-white-50 mb-5">Please enter your login and password!</p>

                            <!-- Web Login Form -->
                            <form action="/webbanhang/account/processLogin" method="POST" class="mb-3">
                                <div class="form-outline form-white mb-4">
                                    <input type="text" name="username" class="form-control form-control-lg" required />
                                    <label class="form-label">UserName</label>
                                </div>

                                <div class="form-outline form-white mb-4">
                                    <input type="password" name="password" class="form-control form-control-lg" required />
                                    <label class="form-label">Password</label>
                                </div>

                                <button class="btn btn-outline-light btn-lg px-5" type="submit">Login (Web)</button>
                            </form>

                            <!-- API Login Form -->
                            <form id="api-login-form">
                                <div class="form-outline form-white mb-4">
                                    <input type="text" name="username" class="form-control form-control-lg" required />
                                    <label class="form-label">UserName (API)</label>
                                </div>

                                <div class="form-outline form-white mb-4">
                                    <input type="password" name="password" class="form-control form-control-lg" required />
                                    <label class="form-label">Password (API)</label>
                                </div>

                                <button class="btn btn-outline-info btn-lg px-5" type="submit">Login (API)</button>
                            </form>

                            <p class="small mb-5 pb-lg-2">
                                <a class="text-white-50" href="#!">Forgot password?</a>
                            </p>

                                <div class="d-flex justify-content-center text-center mt-4 pt-1">
                                    <a href="#!" class="text-white"><i class="fab fa-facebook-f fa-lg"></i></a>
                                    <a href="#!" class="text-white"><i class="fab fa-twitter fa-lg mx-4 px-2"></i></a>
                                    <a href="#!" class="text-white"><i class="fab fa-google fa-lg"></i></a>
                                </div>
                            </div>

                            <div>
                                <p class="mb-0">Don't have an account? 
                                    <a href="/webbanhang/account/register" class="text-white-50 fw-bold">Sign Up</a>
                                </p>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<?php include 'app/views/shares/footer.php'; ?>

<script>
// Xử lý đăng nhập API
document.getElementById('api-login-form').addEventListener('submit', function(e) {
    e.preventDefault();

    const formData = new FormData(this);
    const jsonData = {};
    const messageDiv = document.getElementById('loginMessage');

    formData.forEach((value, key) => {
        jsonData[key] = value;
    });

    // Hiển thị loading
    messageDiv.innerHTML = '<div class="alert alert-info">Đang đăng nhập API...</div>';

    fetch('/webbanhang/account/checkLogin', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(jsonData)
    })
    .then(response => response.json())
    .then(data => {
        if (data.token) {
            localStorage.setItem('jwtToken', data.token);
            messageDiv.innerHTML = '<div class="alert alert-success">Đăng nhập API thành công!</div>';
            setTimeout(() => {
                location.href = '/webbanhang/Product';
            }, 1000);
        } else {
            messageDiv.innerHTML = '<div class="alert alert-danger">Đăng nhập API thất bại: ' + (data.message || 'Lỗi không xác định') + '</div>';
        }
    })
    .catch(error => {
        console.error('Error:', error);
        messageDiv.innerHTML = '<div class="alert alert-danger">Có lỗi xảy ra khi đăng nhập API</div>';
    });
});
</script>