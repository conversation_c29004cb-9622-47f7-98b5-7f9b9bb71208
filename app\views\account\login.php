<style>
    .login-container {
        min-height: 100vh;
        background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
    }

    .login-brand-side {
        background: linear-gradient(135deg, rgba(99, 102, 241, 0.9) 0%, rgba(79, 70, 229, 0.9) 100%);
        position: relative;
        overflow: hidden;
    }

    .login-brand-side::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
        opacity: 0.3;
    }

    .brand-content {
        position: relative;
        z-index: 2;
    }

    .brand-icon {
        font-size: 4rem;
        color: var(--warning-color);
        margin-bottom: 2rem;
    }

    .brand-title {
        font-size: 3rem;
        font-weight: 700;
        margin-bottom: 1rem;
    }

    .brand-subtitle {
        font-size: 1.2rem;
        opacity: 0.9;
        line-height: 1.6;
    }

    .features-list {
        text-align: left;
        max-width: 400px;
    }

    .feature-item {
        display: flex;
        align-items: center;
        margin-bottom: 1rem;
        font-size: 1.1rem;
        opacity: 0.9;
    }

    .login-form-side {
        background: white;
        padding: 3rem 2rem;
    }

    .login-form-container {
        width: 100%;
        max-width: 450px;
    }

    .login-icon {
        font-size: 3rem;
        color: var(--primary-color);
    }

    .login-title {
        font-size: 2.5rem;
        font-weight: 700;
        color: var(--dark-color);
        margin-bottom: 0.5rem;
    }

    .login-subtitle {
        color: var(--text-muted);
        font-size: 1.1rem;
        margin-bottom: 2rem;
    }

    .form-label {
        font-weight: 600;
        color: var(--dark-color);
        margin-bottom: 0.5rem;
    }

    .input-group-text {
        background: var(--light-color);
        border: 1px solid var(--border-color);
        color: var(--text-muted);
    }

    .form-control {
        border: 1px solid var(--border-color);
        padding: 0.75rem 1rem;
        font-size: 1rem;
        transition: all 0.3s ease;
    }

    .form-control:focus {
        border-color: var(--primary-color);
        box-shadow: 0 0 0 0.2rem rgba(99, 102, 241, 0.25);
    }

    .login-btn {
        background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
        border: none;
        padding: 0.875rem 2rem;
        font-weight: 600;
        font-size: 1.1rem;
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
    }

    .login-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(99, 102, 241, 0.3);
    }

    .login-btn:active {
        transform: translateY(0);
    }

    .forgot-password-link {
        color: var(--primary-color);
        text-decoration: none;
        font-weight: 500;
        transition: all 0.3s ease;
    }

    .forgot-password-link:hover {
        color: var(--primary-dark);
        text-decoration: underline;
    }

    .alert-modern {
        border: none;
        border-radius: 0.75rem;
        padding: 1rem 1.25rem;
        font-weight: 500;
    }

    .demo-accounts {
        background: var(--light-color);
        border-radius: 0.75rem;
        padding: 1.5rem;
        margin-top: 2rem;
    }

    .demo-account-btn {
        background: white;
        border: 1px solid var(--border-color);
        border-radius: 0.5rem;
        padding: 0.75rem 1rem;
        margin: 0.25rem;
        transition: all 0.3s ease;
        font-weight: 500;
    }

    .demo-account-btn:hover {
        background: var(--primary-color);
        color: white;
        border-color: var(--primary-color);
        transform: translateY(-1px);
    }

    @media (max-width: 991.98px) {
        .login-brand-side {
            display: none !important;
        }

        .login-form-side {
            padding: 2rem 1rem;
        }

        .brand-title {
            font-size: 2rem;
        }

        .login-title {
            font-size: 2rem;
        }
    }
</style>

<div class="login-container">
    <div class="container-fluid vh-100">
        <div class="row h-100">
            <!-- Left Side - Branding -->
            <div class="col-lg-6 d-none d-lg-flex login-brand-side">
                <div class="d-flex flex-column justify-content-center align-items-center text-white p-5">
                    <div class="brand-content text-center">
                        <div class="brand-icon mb-4">
                            <i class="fas fa-store"></i>
                        </div>
                        <h1 class="brand-title mb-4">Web Bán Hàng</h1>
                        <p class="brand-subtitle mb-5">
                            Hệ thống quản lý bán hàng hiện đại với JWT Authentication
                        </p>
                        <div class="features-list">
                            <div class="feature-item">
                                <i class="fas fa-shield-alt me-3"></i>
                                <span>Bảo mật JWT Authentication</span>
                            </div>
                            <div class="feature-item">
                                <i class="fas fa-api me-3"></i>
                                <span>RESTful API Architecture</span>
                            </div>
                            <div class="feature-item">
                                <i class="fas fa-mobile-alt me-3"></i>
                                <span>Responsive Design</span>
                            </div>
                            <div class="feature-item">
                                <i class="fas fa-chart-line me-3"></i>
                                <span>Real-time Management</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Right Side - Login Form -->
            <div class="col-lg-6 d-flex align-items-center justify-content-center login-form-side">
                <div class="login-form-container">
                    <div class="text-center mb-5">
                        <div class="login-icon mb-3">
                            <i class="fas fa-user-circle"></i>
                        </div>
                        <h2 class="login-title">Đăng nhập</h2>
                        <p class="login-subtitle">Chào mừng bạn quay trở lại!</p>
                    </div>

                    <?php if (isset($error)): ?>
                        <div class="alert alert-danger alert-modern">
                            <i class="fas fa-exclamation-circle me-2"></i>
                            <?= htmlspecialchars($error) ?>
                        </div>
                    <?php endif; ?>

                    <form id="login-form" action="/webbanhang/account/checkLogin" method="POST" class="login-form">
                        <div class="form-group mb-4">
                            <label class="form-label">Tên đăng nhập</label>
                            <div class="input-group">
                                <span class="input-group-text">
                                    <i class="fas fa-user"></i>
                                </span>
                                <input type="text" name="username" class="form-control form-control-lg" required
                                       placeholder="Nhập tên đăng nhập" />
                            </div>
                        </div>

                        <div class="form-group mb-4">
                            <label class="form-label">Mật khẩu</label>
                            <div class="input-group">
                                <span class="input-group-text">
                                    <i class="fas fa-lock"></i>
                                </span>
                                <input type="password" name="password" class="form-control form-control-lg" required
                                       placeholder="Nhập mật khẩu" id="password-input" />
                                <button class="btn btn-outline-secondary" type="button" onclick="togglePassword()">
                                    <i class="fas fa-eye" id="password-toggle-icon"></i>
                                </button>
                            </div>
                        </div>

                        <div class="d-flex justify-content-between align-items-center mb-4">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="remember-me">
                                <label class="form-check-label" for="remember-me">
                                    Ghi nhớ đăng nhập
                                </label>
                            </div>
                            <a href="#" class="forgot-password-link">Quên mật khẩu?</a>
                        </div>

                        <button class="btn btn-primary btn-lg w-100 login-btn" type="submit" id="login-button">
                            <span class="btn-text">
                                <i class="fas fa-sign-in-alt me-2"></i>
                                Đăng nhập
                            </span>
                            <span class="btn-loading d-none">
                                <i class="fas fa-spinner fa-spin me-2"></i>
                                Đang đăng nhập...
                            </span>
                        </button>

                        <!-- Demo Accounts -->
                        <div class="demo-accounts">
                            <h6 class="text-center mb-3 fw-bold text-muted">
                                <i class="fas fa-users me-2"></i>Tài khoản demo
                            </h6>
                            <div class="text-center">
                                <button type="button" class="btn demo-account-btn" onclick="fillDemo('admin', 'admin123')">
                                    <i class="fas fa-crown me-2"></i>Admin
                                </button>
                                <button type="button" class="btn demo-account-btn" onclick="fillDemo('user', 'admin123')">
                                    <i class="fas fa-user me-2"></i>User
                                </button>
                            </div>
                            <p class="text-center text-muted mt-2 mb-0 small">
                                Click để tự động điền thông tin đăng nhập
                            </p>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<?php include 'app/views/shares/footer.php'; ?>

<script>
    // Toggle password visibility
    function togglePassword() {
        const passwordInput = document.getElementById('password-input');
        const toggleIcon = document.getElementById('password-toggle-icon');

        if (passwordInput.type === 'password') {
            passwordInput.type = 'text';
            toggleIcon.classList.remove('fa-eye');
            toggleIcon.classList.add('fa-eye-slash');
        } else {
            passwordInput.type = 'password';
            toggleIcon.classList.remove('fa-eye-slash');
            toggleIcon.classList.add('fa-eye');
        }
    }

    // Fill demo account info
    function fillDemo(username, password) {
        document.querySelector('input[name="username"]').value = username;
        document.querySelector('input[name="password"]').value = password;

        // Add visual feedback
        const demoButtons = document.querySelectorAll('.demo-account-btn');
        demoButtons.forEach(btn => btn.classList.remove('active'));
        event.target.classList.add('active');

        // Show toast
        showToast(`Đã điền thông tin tài khoản ${username}`, 'info');
    }

    // Enhanced login form handler
    document.getElementById('login-form').addEventListener('submit', function(e) {
        e.preventDefault();

        const loginButton = document.getElementById('login-button');
        const btnText = loginButton.querySelector('.btn-text');
        const btnLoading = loginButton.querySelector('.btn-loading');

        // Show loading state
        btnText.classList.add('d-none');
        btnLoading.classList.remove('d-none');
        loginButton.disabled = true;

        const formData = new FormData(this);
        const jsonData = {};

        formData.forEach((value, key) => {
            jsonData[key] = value;
        });

        // Gọi API để lấy JWT token
        fetch('/webbanhang/account/checkLogin', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(jsonData)
        })
        .then(response => response.json())
        .then(data => {
            if (data.token) {
                // Lưu JWT token cho API calls
                localStorage.setItem('jwtToken', data.token);

                // Show success message
                showToast('Đăng nhập thành công! Đang chuyển hướng...', 'success');

                // Smooth redirect
                setTimeout(() => {
                    location.href = '/webbanhang/Product';
                }, 1000);
            } else {
                throw new Error(data.message || 'Đăng nhập thất bại');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showToast('Đăng nhập thất bại: ' + error.message, 'danger');

            // Reset button state
            btnText.classList.remove('d-none');
            btnLoading.classList.add('d-none');
            loginButton.disabled = false;
        });
    });

    // Add enter key support for demo buttons
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Enter' && e.ctrlKey) {
            fillDemo('admin', 'admin123');
        } else if (e.key === 'Enter' && e.shiftKey) {
            fillDemo('user', 'admin123');
        }
    });

    // Add floating animation to brand icon
    document.addEventListener('DOMContentLoaded', function() {
        const brandIcon = document.querySelector('.brand-icon i');
        if (brandIcon) {
            brandIcon.style.animation = 'float 3s ease-in-out infinite';
        }

        // Add fade-in animation to form
        const formContainer = document.querySelector('.login-form-container');
        if (formContainer) {
            formContainer.style.opacity = '0';
            formContainer.style.transform = 'translateY(20px)';
            formContainer.style.transition = 'all 0.6s ease';

            setTimeout(() => {
                formContainer.style.opacity = '1';
                formContainer.style.transform = 'translateY(0)';
            }, 200);
        }
    });

    // Add CSS for floating animation
    const style = document.createElement('style');
    style.textContent = `
        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
        }

        .demo-account-btn.active {
            background: var(--primary-color) !important;
            color: white !important;
            border-color: var(--primary-color) !important;
        }
    `;
    document.head.appendChild(style);
</script>