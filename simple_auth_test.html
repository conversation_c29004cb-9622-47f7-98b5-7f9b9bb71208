<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simple Authentication Test</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-4">
        <h1>🔐 Simple Authentication Test</h1>
        <p class="lead">Một lần đăng nhập, hoạt động cho cả Web và API</p>
        
        <!-- Quick Login -->
        <div class="card mb-4">
            <div class="card-header">
                <h5>🔑 Quick Login</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <button class="btn btn-primary w-100 mb-2" onclick="quickLogin('admin', 'admin123')">Login as Admin</button>
                        <button class="btn btn-secondary w-100 mb-2" onclick="quickLogin('user', 'admin123')">Login as User</button>
                    </div>
                    <div class="col-md-6">
                        <a href="/webbanhang/account/logout" class="btn btn-outline-danger w-100 mb-2">Logout</a>
                        <a href="/webbanhang/account/login" class="btn btn-outline-primary w-100 mb-2">Login Page</a>
                    </div>
                </div>
                <div id="loginResult" class="mt-3"></div>
            </div>
        </div>

        <!-- Test Features -->
        <div class="card mb-4">
            <div class="card-header">
                <h5>🧪 Test Features</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6>Web Interface</h6>
                        <a href="/webbanhang/Product" class="btn btn-outline-info w-100 mb-2" target="_blank">Product List</a>
                        <a href="/webbanhang/Product/add" class="btn btn-outline-success w-100 mb-2" target="_blank">Add Product</a>
                        <a href="/webbanhang/Category" class="btn btn-outline-info w-100 mb-2" target="_blank">Category List</a>
                        <a href="/webbanhang/Category/add" class="btn btn-outline-success w-100 mb-2" target="_blank">Add Category</a>
                    </div>
                    <div class="col-md-6">
                        <h6>API Calls</h6>
                        <button class="btn btn-outline-info w-100 mb-2" onclick="testAPI('GET', '/api/product')">GET Products</button>
                        <button class="btn btn-outline-success w-100 mb-2" onclick="testCreateProduct()">POST Product</button>
                        <button class="btn btn-outline-info w-100 mb-2" onclick="testAPI('GET', '/api/category')">GET Categories</button>
                        <button class="btn btn-outline-success w-100 mb-2" onclick="testCreateCategory()">POST Category</button>
                    </div>
                </div>
                <div id="testResult" class="mt-3"></div>
            </div>
        </div>

        <!-- Expected Results -->
        <div class="card mb-4">
            <div class="card-header">
                <h5>📋 Expected Results</h5>
            </div>
            <div class="card-body">
                <div class="alert alert-info">
                    <h6>✅ Admin Login:</h6>
                    <ul class="mb-0">
                        <li>Can access all /Product/add and /Category/add pages</li>
                        <li>Can call all API endpoints (GET, POST, PUT, DELETE)</li>
                        <li>See all admin buttons in UI</li>
                    </ul>
                </div>
                <div class="alert alert-warning">
                    <h6>⚠️ User Login:</h6>
                    <ul class="mb-0">
                        <li>Cannot access /Product/add and /Category/add pages</li>
                        <li>Cannot call POST/PUT/DELETE API endpoints (403 error)</li>
                        <li>No admin buttons visible in UI</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- Current Status -->
        <div class="card">
            <div class="card-header">
                <h5>📊 Current Status</h5>
            </div>
            <div class="card-body">
                <div id="statusInfo"></div>
                <button class="btn btn-outline-primary" onclick="checkStatus()">Refresh Status</button>
            </div>
        </div>
    </div>

    <script>
        // Quick login function
        async function quickLogin(username, password) {
            try {
                const response = await fetch('/webbanhang/account/checkLogin', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ username, password })
                });

                const data = await response.json();
                
                if (data.token) {
                    localStorage.setItem('jwtToken', data.token);
                    document.getElementById('loginResult').innerHTML = 
                        `<div class="alert alert-success">✅ Logged in as ${username}! Both Session and JWT are active.</div>`;
                    checkStatus();
                    
                    // Refresh page to update session
                    setTimeout(() => {
                        location.reload();
                    }, 1000);
                } else {
                    document.getElementById('loginResult').innerHTML = 
                        `<div class="alert alert-danger">❌ Login failed: ${data.message}</div>`;
                }
            } catch (error) {
                document.getElementById('loginResult').innerHTML = 
                    `<div class="alert alert-danger">❌ Error: ${error.message}</div>`;
            }
        }

        // Test API function
        async function testAPI(method, endpoint) {
            const token = localStorage.getItem('jwtToken');
            try {
                const headers = { 'Content-Type': 'application/json' };
                if (token) {
                    headers['Authorization'] = `Bearer ${token}`;
                }

                const response = await fetch(`/webbanhang${endpoint}`, {
                    method: method,
                    headers: headers
                });

                const data = await response.json();
                const statusClass = response.ok ? 'success' : 'danger';
                
                document.getElementById('testResult').innerHTML = 
                    `<div class="alert alert-${statusClass}">
                        <strong>${method} ${endpoint}</strong><br>
                        Status: ${response.status}<br>
                        Response: ${JSON.stringify(data).substring(0, 150)}...
                    </div>`;
            } catch (error) {
                document.getElementById('testResult').innerHTML = 
                    `<div class="alert alert-danger">❌ Error: ${error.message}</div>`;
            }
        }

        // Test create product
        async function testCreateProduct() {
            const token = localStorage.getItem('jwtToken');
            try {
                const response = await fetch('/webbanhang/api/product', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${token}`
                    },
                    body: JSON.stringify({
                        name: 'Test Product ' + Date.now(),
                        description: 'Created via simple auth test',
                        price: 99000,
                        category_id: 1
                    })
                });

                const data = await response.json();
                const statusClass = response.ok ? 'success' : 'danger';
                
                document.getElementById('testResult').innerHTML = 
                    `<div class="alert alert-${statusClass}">
                        <strong>POST /api/product</strong><br>
                        Status: ${response.status}<br>
                        Response: ${JSON.stringify(data)}
                    </div>`;
            } catch (error) {
                document.getElementById('testResult').innerHTML = 
                    `<div class="alert alert-danger">❌ Error: ${error.message}</div>`;
            }
        }

        // Test create category
        async function testCreateCategory() {
            const token = localStorage.getItem('jwtToken');
            try {
                const response = await fetch('/webbanhang/api/category', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${token}`
                    },
                    body: JSON.stringify({
                        name: 'Test Category ' + Date.now(),
                        description: 'Created via simple auth test'
                    })
                });

                const data = await response.json();
                const statusClass = response.ok ? 'success' : 'danger';
                
                document.getElementById('testResult').innerHTML = 
                    `<div class="alert alert-${statusClass}">
                        <strong>POST /api/category</strong><br>
                        Status: ${response.status}<br>
                        Response: ${JSON.stringify(data)}
                    </div>`;
            } catch (error) {
                document.getElementById('testResult').innerHTML = 
                    `<div class="alert alert-danger">❌ Error: ${error.message}</div>`;
            }
        }

        // Decode JWT
        function decodeJWT(token) {
            try {
                const base64Url = token.split('.')[1];
                const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');
                const jsonPayload = decodeURIComponent(atob(base64).split('').map(function(c) {
                    return '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2);
                }).join(''));
                return JSON.parse(jsonPayload);
            } catch (error) {
                return null;
            }
        }

        // Check status
        function checkStatus() {
            const token = localStorage.getItem('jwtToken');
            let statusHTML = '<h6>Authentication Status:</h6>';
            
            if (token) {
                const decoded = decodeJWT(token);
                if (decoded) {
                    statusHTML += `
                        <div class="alert alert-success">
                            ✅ <strong>Authenticated</strong><br>
                            <strong>User:</strong> ${decoded.data.username}<br>
                            <strong>Role:</strong> ${decoded.data.role}<br>
                            <strong>JWT Expires:</strong> ${new Date(decoded.exp * 1000).toLocaleString()}<br>
                            <small>Both Session and JWT are active for seamless web + API access</small>
                        </div>`;
                } else {
                    statusHTML += '<div class="alert alert-warning">❌ Invalid JWT token</div>';
                }
            } else {
                statusHTML += '<div class="alert alert-secondary">❌ Not authenticated</div>';
            }
            
            document.getElementById('statusInfo').innerHTML = statusHTML;
        }

        // Check status on page load
        checkStatus();
    </script>
</body>
</html>
